"""
配置文件 - 用户转化潜力分析器
"""

# 数据文件配置
DEFAULT_DATA_FILE = "副本用户信息表.xlsx"
OUTPUT_FILE = "用户转化潜力分析结果.xlsx"

# 列名映射配置
COLUMN_MAPPING = {
    '📗心力评分': 'mental_score',
    '📗工作状态': 'work_status', 
    '📗工作年限': 'work_years',
    '📗当前薪资区间': 'salary_range',
    '总金额（自动计算）【总金额大于1000为转化】': 'total_amount'
}

# 分析参数配置
ANALYSIS_CONFIG = {
    'min_sample_size': 10,          # 最小样本量要求
    'conversion_threshold': 1000,    # 转化金额阈值
    'max_dimensions': 2,             # 最大分析维度数
}

# 潜力分级配置
POTENTIAL_LEVELS = {
    'SVIP': {
        'min_conversion_rate': 0.30,
        'min_sample_size': 50
    },
    '高': [
        {'min_conversion_rate': 0.15, 'min_sample_size': 30},
        {'min_conversion_rate': 0.25, 'min_sample_size': 20}
    ],
    '低': {}  # 其他情况
}

# 报告配置
REPORT_CONFIG = {
    'top_n_combinations': 10,        # 显示前N个高潜力组合
    'max_level_display': 5,          # 每个等级最多显示的组合数
    'decimal_places': 2,             # 小数位数
}

# 维度配置
DIMENSIONS = ['mental_score', 'work_status', 'work_years', 'salary_range']

# 潜力分数计算权重
SCORE_WEIGHTS = {
    'conversion_rate_weight': 1.0,
    'sample_size_weight': 1.0,
    'dimension_bonus': 0.1,          # 多维度组合加权
}
