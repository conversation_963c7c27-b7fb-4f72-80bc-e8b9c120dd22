"""
应用自定义分类配置工具
用户可以通过修改参数快速重新生成分类结果
"""

from four_dimension_classifier import FourDimensionClassifier
import pandas as pd

class CustomClassificationApplier:
    """自定义分类配置应用器"""
    
    def __init__(self, file_path="副本用户信息表.xlsx"):
        self.file_path = file_path
        self.classifier = None
        
    def apply_custom_thresholds(self, svip_threshold=0.15, high_threshold=0.08, min_sample_size=10):
        """
        应用自定义阈值
        
        Args:
            svip_threshold: SVIP用户转化率阈值
            high_threshold: 高潜力用户转化率阈值  
            min_sample_size: 最小样本量
        """
        print(f"🔧 应用自定义阈值配置...")
        print(f"  SVIP阈值: {svip_threshold:.1%}")
        print(f"  高潜力阈值: {high_threshold:.1%}")
        print(f"  最小样本量: {min_sample_size}")
        
        # 创建分类器
        self.classifier = FourDimensionClassifier(self.file_path)
        
        # 修改配置
        self.classifier.min_sample_size = min_sample_size
        
        # 加载和预处理数据
        if not self.classifier.load_data() or not self.classifier.clean_and_prepare_data():
            print("❌ 数据加载失败")
            return False
        
        # 进行4维组合分析
        self.classifier.analyze_all_dimension_combinations()
        
        # 应用自定义阈值进行分类
        self._apply_custom_classification_logic(svip_threshold, high_threshold)
        
        # 生成报告
        self.classifier.generate_4d_classification_report()
        self.classifier.generate_4d_html_report()
        
        print("✅ 自定义分类完成!")
        return True
    
    def _apply_custom_classification_logic(self, svip_threshold, high_threshold):
        """应用自定义分类逻辑"""
        print("\n👥 基于自定义阈值进行用户分类...")
        
        # 计算每个用户的最高匹配转化率
        self.classifier.df_clean['user_score_4d'] = 0.0
        
        for _, row in self.classifier.four_dim_results.iterrows():
            dimensions = row['dimensions']
            values = row['values']
            conversion_rate = row['conversion_rate']
            
            # 构建匹配条件
            mask = pd.Series([True] * len(self.classifier.df_clean), index=self.classifier.df_clean.index)
            for dim, val in zip(dimensions, values):
                mask = mask & (self.classifier.df_clean[dim] == val)
            
            # 更新匹配用户的评分（取最高值）
            self.classifier.df_clean.loc[mask, 'user_score_4d'] = pd.Series(
                [max(self.classifier.df_clean.loc[idx, 'user_score_4d'], conversion_rate) 
                 for idx in self.classifier.df_clean.index[mask]],
                index=self.classifier.df_clean.index[mask]
            )
        
        # 基于自定义阈值分类
        self.classifier.df_clean['user_classification_4d'] = '低潜力'
        self.classifier.df_clean.loc[
            self.classifier.df_clean['user_score_4d'] >= high_threshold, 
            'user_classification_4d'
        ] = '高潜力'
        self.classifier.df_clean.loc[
            self.classifier.df_clean['user_score_4d'] >= svip_threshold, 
            'user_classification_4d'
        ] = 'SVIP'
        
        # 更新分类统计
        self.classifier.user_classifications = self.classifier.df_clean['user_classification_4d'].value_counts()
        
        print(f"自定义分类完成:")
        total = len(self.classifier.df_clean)
        for level, count in self.classifier.user_classifications.items():
            percentage = count / total * 100
            level_users = self.classifier.df_clean[self.classifier.df_clean['user_classification_4d'] == level]
            actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
            print(f"  {level}: {count} 人 ({percentage:.1f}%) - 实际转化率: {actual_conversion:.1%}")
    
    def quick_adjustment_scenarios(self):
        """快速调整场景"""
        print("\n🎯 快速调整场景")
        print("="*50)
        
        scenarios = {
            '1': {
                'name': '提高精准度 (减少SVIP数量，提高质量)',
                'svip_threshold': 0.20,
                'high_threshold': 0.12,
                'description': '适用于资源有限，需要重点关注最高价值用户'
            },
            '2': {
                'name': '扩大覆盖面 (增加SVIP数量，扩大营销范围)',
                'svip_threshold': 0.12,
                'high_threshold': 0.06,
                'description': '适用于营销资源充足，希望扩大目标用户群'
            },
            '3': {
                'name': '平衡策略 (标准配置)',
                'svip_threshold': 0.15,
                'high_threshold': 0.08,
                'description': '平衡精准度和覆盖面的标准配置'
            },
            '4': {
                'name': '保守策略 (高门槛，确保转化)',
                'svip_threshold': 0.25,
                'high_threshold': 0.15,
                'description': '适用于转化成本高，需要确保高转化率'
            },
            '5': {
                'name': '激进策略 (低门槛，最大化机会)',
                'svip_threshold': 0.10,
                'high_threshold': 0.05,
                'description': '适用于新产品推广，需要快速获取用户'
            }
        }
        
        for key, scenario in scenarios.items():
            print(f"{key}. {scenario['name']}")
            print(f"   SVIP阈值: {scenario['svip_threshold']:.1%} | 高潜力阈值: {scenario['high_threshold']:.1%}")
            print(f"   说明: {scenario['description']}")
            print()
        
        choice = input("请选择场景 (1-5) 或按回车使用标准配置: ").strip()
        
        if choice in scenarios:
            selected = scenarios[choice]
            print(f"\n✅ 已选择: {selected['name']}")
            return selected['svip_threshold'], selected['high_threshold']
        else:
            print("\n✅ 使用标准配置")
            return 0.15, 0.08
    
    def interactive_adjustment(self):
        """交互式调整"""
        print("\n🔧 交互式参数调整")
        print("="*40)
        
        # 获取当前配置
        current_svip = 0.15
        current_high = 0.08
        current_sample = 10
        
        print(f"当前配置:")
        print(f"  SVIP阈值: {current_svip:.1%}")
        print(f"  高潜力阈值: {current_high:.1%}")
        print(f"  最小样本量: {current_sample}")
        print()
        
        # 用户输入新配置
        svip_input = input(f"新的SVIP阈值 (当前{current_svip:.1%}, 建议10%-30%): ").strip()
        if svip_input:
            try:
                if svip_input.endswith('%'):
                    current_svip = float(svip_input[:-1]) / 100
                else:
                    current_svip = float(svip_input)
            except ValueError:
                print("⚠️ 输入格式错误，使用默认值")
        
        high_input = input(f"新的高潜力阈值 (当前{current_high:.1%}, 建议5%-15%): ").strip()
        if high_input:
            try:
                if high_input.endswith('%'):
                    current_high = float(high_input[:-1]) / 100
                else:
                    current_high = float(high_input)
            except ValueError:
                print("⚠️ 输入格式错误，使用默认值")
        
        sample_input = input(f"新的最小样本量 (当前{current_sample}, 建议5-20): ").strip()
        if sample_input:
            try:
                current_sample = int(sample_input)
            except ValueError:
                print("⚠️ 输入格式错误，使用默认值")
        
        return current_svip, current_high, current_sample
    
    def compare_configurations(self, configs):
        """比较不同配置的效果"""
        print("\n📊 配置效果对比")
        print("="*60)
        
        results = []
        
        for i, (name, svip_thresh, high_thresh) in enumerate(configs):
            print(f"\n测试配置 {i+1}: {name}")
            
            # 临时分类器
            temp_classifier = FourDimensionClassifier(self.file_path)
            temp_classifier.min_sample_size = 10
            
            if temp_classifier.load_data() and temp_classifier.clean_and_prepare_data():
                temp_classifier.analyze_all_dimension_combinations()
                
                # 应用配置
                temp_classifier.df_clean['user_score_4d'] = 0.0
                for _, row in temp_classifier.four_dim_results.iterrows():
                    dimensions = row['dimensions']
                    values = row['values']
                    conversion_rate = row['conversion_rate']
                    
                    mask = pd.Series([True] * len(temp_classifier.df_clean), index=temp_classifier.df_clean.index)
                    for dim, val in zip(dimensions, values):
                        mask = mask & (temp_classifier.df_clean[dim] == val)
                    
                    temp_classifier.df_clean.loc[mask, 'user_score_4d'] = pd.Series(
                        [max(temp_classifier.df_clean.loc[idx, 'user_score_4d'], conversion_rate) 
                         for idx in temp_classifier.df_clean.index[mask]],
                        index=temp_classifier.df_clean.index[mask]
                    )
                
                # 分类
                temp_classifier.df_clean['user_classification_4d'] = '低潜力'
                temp_classifier.df_clean.loc[
                    temp_classifier.df_clean['user_score_4d'] >= high_thresh, 
                    'user_classification_4d'
                ] = '高潜力'
                temp_classifier.df_clean.loc[
                    temp_classifier.df_clean['user_score_4d'] >= svip_thresh, 
                    'user_classification_4d'
                ] = 'SVIP'
                
                # 统计结果
                classifications = temp_classifier.df_clean['user_classification_4d'].value_counts()
                total = len(temp_classifier.df_clean)
                
                result = {
                    'name': name,
                    'svip_count': classifications.get('SVIP', 0),
                    'svip_percent': classifications.get('SVIP', 0) / total * 100,
                    'high_count': classifications.get('高潜力', 0),
                    'high_percent': classifications.get('高潜力', 0) / total * 100,
                    'low_count': classifications.get('低潜力', 0),
                    'low_percent': classifications.get('低潜力', 0) / total * 100
                }
                
                results.append(result)
                
                print(f"  SVIP: {result['svip_count']} 人 ({result['svip_percent']:.1f}%)")
                print(f"  高潜力: {result['high_count']} 人 ({result['high_percent']:.1f}%)")
                print(f"  低潜力: {result['low_count']} 人 ({result['low_percent']:.1f}%)")
        
        return results

def main():
    """主函数"""
    print("🎯 4维分类自定义配置工具")
    print("="*50)
    
    applier = CustomClassificationApplier()
    
    print("\n请选择操作模式:")
    print("1. 快速场景选择")
    print("2. 交互式参数调整")
    print("3. 配置效果对比")
    print("4. 直接应用标准配置")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == '1':
        svip_thresh, high_thresh = applier.quick_adjustment_scenarios()
        applier.apply_custom_thresholds(svip_thresh, high_thresh)
    
    elif choice == '2':
        svip_thresh, high_thresh, min_sample = applier.interactive_adjustment()
        applier.apply_custom_thresholds(svip_thresh, high_thresh, min_sample)
    
    elif choice == '3':
        configs = [
            ('保守策略', 0.25, 0.15),
            ('标准配置', 0.15, 0.08),
            ('激进策略', 0.10, 0.05)
        ]
        applier.compare_configurations(configs)
    
    elif choice == '4':
        applier.apply_custom_thresholds()
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
