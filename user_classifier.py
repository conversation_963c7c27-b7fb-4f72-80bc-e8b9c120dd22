"""
用户分类器 - 基于转化潜力分析结果自动分类用户
"""

import pandas as pd
import numpy as np
from user_conversion_analyzer import UserConversionAnalyzer
from typing import Dict, List, Tuple

class UserClassifier(UserConversionAnalyzer):
    """用户分类器 - 将用户分为SVIP、高潜力、低潜力三类"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.classification_rules = {}
        self.user_classifications = None
        
    def find_optimal_classification_rules(self):
        """找到最佳的分类依据"""
        if not hasattr(self, 'results_df'):
            print("请先完成标签组合分析")
            return

        print("\n🔍 分析最佳分类依据...")

        # 按转化率排序，获取所有组合
        sorted_results = self.results_df.sort_values('conversion_rate', ascending=False)

        # 计算分位数来确定分类阈值
        total_combinations = len(sorted_results)

        # 动态确定分类阈值
        conversion_rates = sorted_results['conversion_rate'].values

        # 使用分位数方法确定阈值
        svip_threshold = np.percentile(conversion_rates, 90)  # 前10%
        high_threshold = np.percentile(conversion_rates, 70)  # 前30%

        # 确保阈值合理
        svip_threshold = max(svip_threshold, 0.15)  # SVIP至少15%转化率
        high_threshold = max(high_threshold, 0.08)  # 高潜力至少8%转化率

        print(f"分类阈值: SVIP≥{svip_threshold:.1%}, 高潜力≥{high_threshold:.1%}")

        # 根据阈值分类组合
        svip_combinations = sorted_results[sorted_results['conversion_rate'] >= svip_threshold]
        high_combinations = sorted_results[
            (sorted_results['conversion_rate'] >= high_threshold) &
            (sorted_results['conversion_rate'] < svip_threshold)
        ]

        # 生成分类规则
        classification_rules = {
            'SVIP': self._extract_rules_from_combinations(svip_combinations),
            '高潜力': self._extract_rules_from_combinations(high_combinations),
            '低潜力': []  # 其他情况
        }

        self.classification_rules = classification_rules
        return classification_rules
    
    def _extract_rules_from_combinations(self, combinations_df):
        """从组合数据中提取分类规则"""
        rules = []

        # 优先选择样本量大且转化率高的组合
        for _, row in combinations_df.head(5).iterrows():  # 每个等级最多5个规则
            rule_text = row['combination']
            stats = {
                'avg_conversion': row['conversion_rate'],
                'total_sample': row['sample_size'],
                'count': 1
            }
            rules.append((rule_text, stats))

        return rules
    
    def classify_users_by_score(self):
        """基于综合评分对用户进行分类"""
        if not hasattr(self, 'df_clean'):
            print("请先进行数据预处理")
            return

        print("\n👥 基于综合评分进行用户分类...")

        # 计算每个用户的潜力评分
        self.df_clean['user_score'] = 0.0

        # 为每个用户计算匹配的最高转化率组合
        for _, row in self.results_df.iterrows():
            combination = row['combination']
            conversion_rate = row['conversion_rate']

            # 解析组合并匹配用户
            mask = self._match_combination(combination)

            # 更新匹配用户的评分（取最高值）
            self.df_clean.loc[mask, 'user_score'] = np.maximum(
                self.df_clean.loc[mask, 'user_score'],
                conversion_rate
            )

        # 基于评分分类
        score_percentiles = np.percentile(self.df_clean['user_score'], [70, 90])
        high_threshold = score_percentiles[0]
        svip_threshold = score_percentiles[1]

        # 确保阈值合理
        high_threshold = max(high_threshold, 0.08)
        svip_threshold = max(svip_threshold, 0.15)

        # 分类
        self.df_clean['user_classification'] = '低潜力'
        self.df_clean.loc[self.df_clean['user_score'] >= high_threshold, 'user_classification'] = '高潜力'
        self.df_clean.loc[self.df_clean['user_score'] >= svip_threshold, 'user_classification'] = 'SVIP'

        return self.df_clean['user_classification'].value_counts()

    def _match_combination(self, combination):
        """匹配组合条件的用户"""
        try:
            if ' & ' in combination:
                # 多维度组合
                parts = combination.split(' & ')
                conditions = []
                for part in parts:
                    dim, val = part.split('=')
                    conditions.append(self.df_clean[dim] == val)

                # 应用所有条件
                mask = conditions[0]
                for condition in conditions[1:]:
                    mask = mask & condition
                return mask
            else:
                # 单维度组合
                dim, val = combination.split('=')
                return self.df_clean[dim] == val
        except:
            return pd.Series([False] * len(self.df_clean), index=self.df_clean.index)
    
    def classify_users(self):
        """对所有用户进行分类"""
        # 使用综合评分方法进行分类
        classification_stats = self.classify_users_by_score()

        print(f"用户分类完成:")
        for level, count in classification_stats.items():
            percentage = count / len(self.df_clean) * 100
            print(f"  {level}: {count} 人 ({percentage:.1f}%)")

        self.user_classifications = classification_stats
        return classification_stats
    
    def _apply_classification_rule(self, rule_text, level):
        """应用单个分类规则"""
        try:
            if ' & ' in rule_text:
                # 多维度规则
                parts = rule_text.split(' & ')
                conditions = []
                for part in parts:
                    dim, val = part.split('=')
                    conditions.append(self.df_clean[dim] == val)
                
                # 应用所有条件
                mask = conditions[0]
                for condition in conditions[1:]:
                    mask = mask & condition
                
                self.df_clean.loc[mask, 'user_classification'] = level
            else:
                # 单维度规则
                dim, val = rule_text.split('=')
                mask = self.df_clean[dim] == val
                self.df_clean.loc[mask, 'user_classification'] = level
        except Exception as e:
            print(f"应用规则 {rule_text} 时出错: {e}")
    
    def generate_classification_report(self):
        """生成分类报告"""
        if self.user_classifications is None:
            print("请先完成用户分类")
            return
        
        print("\n" + "="*60)
        print("🎯 用户分类依据报告")
        print("="*60)
        
        # 显示最佳分类规则
        print("\n📋 最佳分类依据:")
        for level in ['SVIP', '高潜力', '低潜力']:
            if level in self.classification_rules and self.classification_rules[level]:
                print(f"\n🏆 {level}用户识别标准:")
                for i, (rule_text, stats) in enumerate(self.classification_rules[level], 1):
                    avg_conversion = stats['avg_conversion']
                    total_sample = stats['total_sample']
                    print(f"  {i}. {rule_text}")
                    print(f"     转化率: {avg_conversion:.1%} | 样本量: {total_sample}")
        
        # 显示分类结果统计
        print(f"\n📊 用户分类结果:")
        total_users = len(self.df_clean)
        for level, count in self.user_classifications.items():
            percentage = count / total_users * 100
            # 计算该分类的实际转化率
            level_users = self.df_clean[self.df_clean['user_classification'] == level]
            actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
            print(f"  {level}: {count} 人 ({percentage:.1f}%) - 实际转化率: {actual_conversion:.1%}")
        
        # 保存分类结果
        self._save_classification_results()
        
        print(f"\n💾 用户分类结果已保存到: 用户分类结果.xlsx")
    
    def _save_classification_results(self):
        """保存分类结果到Excel"""
        # 准备输出数据
        output_df = self.df_clean.copy()
        
        # 添加原始列名用于识别
        original_columns = {
            'mental_score': '📗心力评分',
            'work_status': '📗工作状态',
            'work_years': '📗工作年限',
            'salary_range': '📗当前薪资区间'
        }
        
        for _, orig_col in original_columns.items():
            if orig_col in self.df.columns:
                output_df[orig_col] = self.df[orig_col]
        
        # 重新排列列顺序
        columns_order = ['user_classification'] + list(original_columns.values()) + \
                       ['total_amount', 'is_converted']
        
        # 只保留存在的列
        existing_columns = [col for col in columns_order if col in output_df.columns]
        output_df = output_df[existing_columns]
        
        # 保存到Excel
        with pd.ExcelWriter('用户分类结果.xlsx', engine='openpyxl') as writer:
            # 用户分类结果
            output_df.to_excel(writer, sheet_name='用户分类', index=False)
            
            # 分类统计
            stats_df = pd.DataFrame({
                '分类等级': self.user_classifications.index,
                '用户数量': self.user_classifications.values,
                '占比': [f"{v/len(self.df_clean)*100:.1f}%" for v in self.user_classifications.values]
            })
            stats_df.to_excel(writer, sheet_name='分类统计', index=False)
            
            # 分类规则
            rules_data = []
            for level, rules in self.classification_rules.items():
                for rule_text, stats in rules:
                    rules_data.append({
                        '分类等级': level,
                        '分类规则': rule_text,
                        '平均转化率': f"{stats['avg_conversion']:.1%}",
                        '样本量': stats['total_sample']
                    })
            
            if rules_data:
                rules_df = pd.DataFrame(rules_data)
                rules_df.to_excel(writer, sheet_name='分类规则', index=False)
    
    def run_complete_classification(self):
        """运行完整的分类流程"""
        print("🚀 开始完整的用户分类分析...")
        
        # 1. 加载和预处理数据
        if not self.load_data():
            return False
        
        if not self.clean_and_prepare_data():
            return False
        
        # 2. 进行标签组合分析
        self.analyze_label_combinations()
        self.calculate_potential_score()
        
        # 3. 找到最佳分类依据
        self.find_optimal_classification_rules()
        
        # 4. 对用户进行分类
        self.classify_users()
        
        # 5. 生成分类报告
        self.generate_classification_report()
        
        return True

def main():
    """主函数"""
    file_path = "副本用户信息表.xlsx"
    
    # 创建用户分类器
    classifier = UserClassifier(file_path)
    
    # 运行完整分类流程
    classifier.run_complete_classification()

if __name__ == "__main__":
    main()
