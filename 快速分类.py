"""
快速用户分类脚本 - 一键获取用户分类依据
"""

from user_classifier import UserClassifier
import pandas as pd

def quick_classify(file_path="副本用户信息表.xlsx"):
    """
    快速分类用户并输出结果
    
    Args:
        file_path: Excel文件路径
    """
    print("🚀 开始快速用户分类...")
    print("="*50)
    
    # 创建分类器
    classifier = UserClassifier(file_path)
    
    # 运行完整分类
    success = classifier.run_complete_classification()
    
    if success:
        print("\n✅ 分类完成！")
        print("\n📁 生成的文件:")
        print("  - 用户分类结果.xlsx (详细分类数据)")
        print("  - 用户分类依据总结.md (分类依据说明)")
        
        # 显示简化的分类依据
        print_simple_classification_rules(classifier)
        
    else:
        print("❌ 分类失败，请检查数据文件")

def print_simple_classification_rules(classifier):
    """打印简化的分类依据"""
    print("\n" + "="*50)
    print("🎯 用户分类依据 (简化版)")
    print("="*50)
    
    if hasattr(classifier, 'user_classifications'):
        total = sum(classifier.user_classifications.values())
        
        print(f"\n📊 分类结果:")
        for level, count in classifier.user_classifications.items():
            percentage = count / total * 100
            print(f"  {level}: {count:,} 人 ({percentage:.1f}%)")
        
        print(f"\n🎯 核心分类依据:")
        print(f"  SVIP用户 (0.8%):")
        print(f"    • 工作1-3年 + 薪资25001-40000 (转化率25%)")
        print(f"    • 心力评分5分 + 薪资40000以上 (转化率18%)")
        print(f"    • 待业状态 + 薪资25001-40000 (转化率15%)")
        
        print(f"  高潜力用户 (26.3%):")
        print(f"    • 心力评分3分 + 薪资20001-25000 (转化率14%)")
        print(f"    • 待业状态 + 薪资20001-25000 (转化率13%)")
        print(f"    • 心力评分2-3分 + 薪资25001-40000 (转化率12%)")
        
        print(f"  低潜力用户 (73.0%):")
        print(f"    • 其他不符合上述条件的用户组合")

def analyze_specific_user_group(file_path="副本用户信息表.xlsx", 
                               mental_score=None, 
                               work_status=None, 
                               work_years=None, 
                               salary_range=None):
    """
    分析特定用户群体的转化情况
    
    Args:
        file_path: 数据文件路径
        mental_score: 心力评分筛选条件
        work_status: 工作状态筛选条件  
        work_years: 工作年限筛选条件
        salary_range: 薪资区间筛选条件
    """
    print(f"🔍 分析特定用户群体...")
    
    # 加载数据
    classifier = UserClassifier(file_path)
    if not classifier.load_data() or not classifier.clean_and_prepare_data():
        print("❌ 数据加载失败")
        return
    
    # 应用筛选条件
    df = classifier.df_clean.copy()
    conditions = []
    filter_desc = []
    
    if mental_score is not None:
        conditions.append(df['mental_score'] == mental_score)
        filter_desc.append(f"心力评分={mental_score}")
    
    if work_status is not None:
        conditions.append(df['work_status'] == work_status)
        filter_desc.append(f"工作状态={work_status}")
    
    if work_years is not None:
        conditions.append(df['work_years'] == work_years)
        filter_desc.append(f"工作年限={work_years}")
    
    if salary_range is not None:
        conditions.append(df['salary_range'] == salary_range)
        filter_desc.append(f"薪资区间={salary_range}")
    
    if conditions:
        # 应用所有条件
        mask = conditions[0]
        for condition in conditions[1:]:
            mask = mask & condition
        
        filtered_df = df[mask]
        
        if len(filtered_df) > 0:
            conversion_rate = filtered_df['is_converted'].mean()
            total_users = len(filtered_df)
            converted_users = filtered_df['is_converted'].sum()
            
            print(f"\n📋 筛选条件: {' & '.join(filter_desc)}")
            print(f"📊 分析结果:")
            print(f"  总用户数: {total_users:,}")
            print(f"  转化用户数: {converted_users}")
            print(f"  转化率: {conversion_rate:.2%}")
            
            # 判断潜力等级
            if conversion_rate >= 0.15:
                level = "SVIP"
            elif conversion_rate >= 0.08:
                level = "高潜力"
            else:
                level = "低潜力"
            
            print(f"  潜力等级: {level}")
        else:
            print(f"❌ 未找到符合条件的用户: {' & '.join(filter_desc)}")
    else:
        print("❌ 请至少指定一个筛选条件")

if __name__ == "__main__":
    # 快速分类示例
    quick_classify()
    
    print("\n" + "="*50)
    print("🔍 特定群体分析示例")
    print("="*50)
    
    # 分析特定群体示例
    analyze_specific_user_group(
        mental_score=3,
        salary_range="20001-25000"
    )
    
    analyze_specific_user_group(
        work_years="1-3年",
        salary_range="25001-40000"
    )
    
    analyze_specific_user_group(
        work_status="待业",
        salary_range="25001-40000"
    )
