"""
增强版用户转化潜力分析器 - 包含数据可视化功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from user_conversion_analyzer import UserConversionAnalyzer
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class EnhancedUserConversionAnalyzer(UserConversionAnalyzer):
    """增强版用户转化潜力分析器，包含可视化功能"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.visualization_data = {}
    
    def create_visualizations(self):
        """创建数据可视化图表"""
        if not hasattr(self, 'df_clean') or not hasattr(self, 'results_df'):
            print("请先完成数据分析")
            return
        
        print("\n生成数据可视化图表...")
        
        # 创建图表目录
        import os
        if not os.path.exists('charts'):
            os.makedirs('charts')
        
        # 1. 总体转化率分布
        self._plot_overall_conversion()
        
        # 2. 各维度转化率对比
        self._plot_dimension_conversion()
        
        # 3. 潜力等级分布
        self._plot_potential_distribution()
        
        # 4. TOP组合转化率
        self._plot_top_combinations()
        
        # 5. 样本量vs转化率散点图
        self._plot_sample_vs_conversion()
        
        print("可视化图表已保存到 charts/ 目录")
    
    def _plot_overall_conversion(self):
        """绘制总体转化率分布"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 转化用户分布饼图
        conversion_counts = self.df_clean['is_converted'].value_counts()
        labels = ['未转化', '已转化']
        colors = ['#ff9999', '#66b3ff']
        
        ax1.pie(conversion_counts.values, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('用户转化分布', fontsize=14, fontweight='bold')
        
        # 转化金额分布直方图
        ax2.hist(self.df_clean['total_amount'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(x=1000, color='red', linestyle='--', linewidth=2, label='转化阈值 (1000)')
        ax2.set_xlabel('总金额')
        ax2.set_ylabel('用户数量')
        ax2.set_title('用户消费金额分布', fontsize=14, fontweight='bold')
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig('charts/overall_conversion.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_dimension_conversion(self):
        """绘制各维度转化率对比"""
        dimensions = ['mental_score', 'work_status', 'work_years', 'salary_range']
        dimension_names = ['心力评分', '工作状态', '工作年限', '薪资区间']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for i, (dim, name) in enumerate(zip(dimensions, dimension_names)):
            # 计算各维度值的转化率
            conversion_by_dim = self.df_clean.groupby(dim)['is_converted'].agg(['count', 'mean']).reset_index()
            conversion_by_dim = conversion_by_dim[conversion_by_dim['count'] >= 10]  # 过滤样本量太小的
            
            # 绘制柱状图
            bars = axes[i].bar(range(len(conversion_by_dim)), conversion_by_dim['mean'], 
                              color='lightcoral', alpha=0.7, edgecolor='black')
            
            # 添加数值标签
            for j, bar in enumerate(bars):
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{height:.1%}', ha='center', va='bottom', fontsize=10)
            
            axes[i].set_xlabel(f'{name}')
            axes[i].set_ylabel('转化率')
            axes[i].set_title(f'{name}转化率分析', fontsize=12, fontweight='bold')
            axes[i].set_xticks(range(len(conversion_by_dim)))
            axes[i].set_xticklabels(conversion_by_dim[dim], rotation=45, ha='right')
            axes[i].set_ylim(0, max(conversion_by_dim['mean']) * 1.2)
        
        plt.tight_layout()
        plt.savefig('charts/dimension_conversion.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_potential_distribution(self):
        """绘制潜力等级分布"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 潜力等级分布饼图
        level_counts = self.results_df['potential_level'].value_counts()
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
        
        ax1.pie(level_counts.values, labels=level_counts.index, colors=colors, 
               autopct='%1.1f%%', startangle=90)
        ax1.set_title('潜力等级分布', fontsize=14, fontweight='bold')
        
        # 各等级平均转化率
        avg_conversion = self.results_df.groupby('potential_level')['conversion_rate'].mean()
        bars = ax2.bar(avg_conversion.index, avg_conversion.values, 
                      color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.7, edgecolor='black')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{height:.1%}', ha='center', va='bottom', fontsize=12, fontweight='bold')
        
        ax2.set_xlabel('潜力等级')
        ax2.set_ylabel('平均转化率')
        ax2.set_title('各潜力等级平均转化率', fontsize=14, fontweight='bold')
        ax2.set_ylim(0, max(avg_conversion.values) * 1.2)
        
        plt.tight_layout()
        plt.savefig('charts/potential_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_top_combinations(self):
        """绘制TOP组合转化率"""
        top_10 = self.results_df.head(10)
        
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 创建水平柱状图
        y_pos = np.arange(len(top_10))
        bars = ax.barh(y_pos, top_10['conversion_rate'], color='lightgreen', alpha=0.7, edgecolor='black')
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.01, bar.get_y() + bar.get_height()/2.,
                   f'{width:.1%}', ha='left', va='center', fontsize=10, fontweight='bold')
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels([combo[:50] + '...' if len(combo) > 50 else combo 
                           for combo in top_10['combination']], fontsize=10)
        ax.set_xlabel('转化率')
        ax.set_title('TOP 10 高潜力标签组合', fontsize=14, fontweight='bold')
        ax.set_xlim(0, max(top_10['conversion_rate']) * 1.2)
        
        plt.tight_layout()
        plt.savefig('charts/top_combinations.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_sample_vs_conversion(self):
        """绘制样本量vs转化率散点图"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 根据潜力等级设置颜色
        colors = {'SVIP': '#ff6b6b', '高': '#4ecdc4', '低': '#95a5a6'}
        
        for level in self.results_df['potential_level'].unique():
            level_data = self.results_df[self.results_df['potential_level'] == level]
            ax.scatter(level_data['sample_size'], level_data['conversion_rate'], 
                      c=colors[level], label=f'{level}潜力', alpha=0.7, s=60)
        
        ax.set_xlabel('样本量')
        ax.set_ylabel('转化率')
        ax.set_title('样本量 vs 转化率分布', fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加潜力分级线
        ax.axhline(y=0.3, color='red', linestyle='--', alpha=0.5, label='SVIP阈值')
        ax.axhline(y=0.15, color='orange', linestyle='--', alpha=0.5, label='高潜力阈值')
        
        plt.tight_layout()
        plt.savefig('charts/sample_vs_conversion.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_enhanced_report(self):
        """生成增强版分析报告"""
        # 先生成基础报告
        super().generate_report()
        
        # 添加可视化
        self.create_visualizations()
        
        # 生成HTML报告
        self._generate_html_report()
    
    def _generate_html_report(self):
        """生成HTML格式的报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>用户转化潜力分析报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                h1 {{ color: #333; text-align: center; }}
                h2 {{ color: #666; border-bottom: 2px solid #ddd; }}
                .summary {{ background-color: #f9f9f9; padding: 20px; border-radius: 5px; }}
                .chart {{ text-align: center; margin: 20px 0; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>用户转化潜力分析报告</h1>
            
            <div class="summary">
                <h2>分析概要</h2>
                <p><strong>总用户数:</strong> {len(self.df_clean)}</p>
                <p><strong>转化用户数:</strong> {self.df_clean['is_converted'].sum()}</p>
                <p><strong>总体转化率:</strong> {self.df_clean['is_converted'].mean():.2%}</p>
                <p><strong>分析组合数:</strong> {len(self.results_df)}</p>
            </div>
            
            <h2>数据可视化</h2>
            <div class="chart">
                <h3>总体转化分布</h3>
                <img src="charts/overall_conversion.png" alt="总体转化分布" style="max-width: 100%;">
            </div>
            
            <div class="chart">
                <h3>各维度转化率分析</h3>
                <img src="charts/dimension_conversion.png" alt="各维度转化率" style="max-width: 100%;">
            </div>
            
            <div class="chart">
                <h3>潜力等级分布</h3>
                <img src="charts/potential_distribution.png" alt="潜力等级分布" style="max-width: 100%;">
            </div>
            
            <div class="chart">
                <h3>TOP 10 高潜力组合</h3>
                <img src="charts/top_combinations.png" alt="TOP组合" style="max-width: 100%;">
            </div>
            
            <div class="chart">
                <h3>样本量与转化率关系</h3>
                <img src="charts/sample_vs_conversion.png" alt="样本量vs转化率" style="max-width: 100%;">
            </div>
            
            <h2>详细数据</h2>
            <p>完整的分析数据请查看: <a href="用户转化潜力分析结果.xlsx">用户转化潜力分析结果.xlsx</a></p>
            
        </body>
        </html>
        """
        
        with open('用户转化潜力分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("📄 HTML报告已生成: 用户转化潜力分析报告.html")

def main():
    """主函数"""
    file_path = "副本用户信息表.xlsx"
    
    # 创建增强版分析器实例
    analyzer = EnhancedUserConversionAnalyzer(file_path)
    
    # 加载数据
    if analyzer.load_data():
        # 执行完整分析
        analyzer.analyze_conversion_potential()
        # 生成增强版报告
        analyzer.generate_enhanced_report()
    else:
        print("数据加载失败，请检查文件路径和格式")

if __name__ == "__main__":
    main()
