"""
4维分类配置文件 - 可调整的分类参数和策略
用户可以修改这些参数来重新生成分类结果
"""

# ==================== 分类阈值配置 ====================

# SVIP用户识别阈值
SVIP_CONVERSION_THRESHOLD = 0.15  # 转化率≥15%
SVIP_MIN_SAMPLE_SIZE = 10         # 最小样本量

# 高潜力用户识别阈值  
HIGH_POTENTIAL_CONVERSION_THRESHOLD = 0.08  # 转化率≥8%
HIGH_POTENTIAL_MIN_SAMPLE_SIZE = 10          # 最小样本量

# 低潜力用户识别阈值
LOW_POTENTIAL_CONVERSION_THRESHOLD = 0.08   # 转化率<8%

# ==================== 维度权重配置 ====================

# 各维度在分类中的重要性权重 (总和应为100%)
DIMENSION_WEIGHTS = {
    'salary_range': 0.40,    # 薪资区间权重 40%
    'work_years': 0.25,      # 工作年限权重 25%
    'work_status': 0.20,     # 工作状态权重 20%
    'mental_score': 0.15     # 心力评分权重 15%
}

# ==================== 营销策略配置 ====================

# SVIP用户营销策略
SVIP_STRATEGY = {
    'name': '一对一个性化服务策略',
    'tactics': [
        '专属客户经理对接',
        '定制化产品方案',
        '优先级最高的服务响应',
        '高价值产品推荐',
        'VIP专属活动邀请',
        '个性化咨询服务'
    ],
    'investment_level': '高投入',
    'expected_roi': '300-500%',
    'contact_frequency': '每周1-2次',
    'channel_priority': ['电话', '微信', '邮件']
}

# 高潜力用户营销策略
HIGH_POTENTIAL_STRATEGY = {
    'name': '培育式精准营销策略',
    'tactics': [
        '定期价值内容推送',
        '阶段性产品试用机会',
        '群体化营销活动',
        '中等频次的个性化推荐',
        '转化路径引导',
        '社群运营参与'
    ],
    'investment_level': '中等投入',
    'expected_roi': '150-250%',
    'contact_frequency': '每月2-3次',
    'channel_priority': ['微信', '邮件', '短信']
}

# 低潜力用户营销策略
LOW_POTENTIAL_STRATEGY = {
    'name': '低成本维护策略',
    'tactics': [
        '基础内容推送',
        '自动化营销流程',
        '低频次群发活动',
        '标准化产品推荐',
        '被动式服务响应',
        '长期培育计划'
    ],
    'investment_level': '低投入',
    'expected_roi': '50-100%',
    'contact_frequency': '每季度1-2次',
    'channel_priority': ['邮件', '短信', '公众号']
}

# ==================== 特殊规则配置 ====================

# 特殊用户群体的额外规则
SPECIAL_RULES = {
    # 高薪资但低转化率的特殊处理
    'high_salary_low_conversion': {
        'condition': 'salary_range in ["40000以上"] and conversion_rate < 0.10',
        'action': '降级到高潜力，增加个性化接触',
        'reasoning': '高薪资用户可能需要更精准的产品匹配'
    },
    
    # 职业变动期的特殊关注
    'career_transition': {
        'condition': 'work_status in ["已提交离职", "待业"]',
        'action': '提升关注度，增加接触频次',
        'reasoning': '职业变动期是最佳营销时机'
    },
    
    # 年轻高潜力群体的长期价值
    'young_high_potential': {
        'condition': 'work_years in ["0-1年", "1-3年"] and salary_range in ["20001-25000", "25001-40000"]',
        'action': '重点培育，建立长期关系',
        'reasoning': '年轻高薪群体具有巨大的长期价值'
    }
}

# ==================== 动态调整规则 ====================

# 基于业务目标的动态调整
DYNAMIC_ADJUSTMENT = {
    # 如果目标是提高整体转化率
    'maximize_conversion': {
        'svip_threshold': 0.20,      # 提高SVIP门槛
        'high_threshold': 0.10,      # 提高高潜力门槛
        'focus': 'precision'         # 注重精准度
    },
    
    # 如果目标是扩大用户覆盖
    'maximize_coverage': {
        'svip_threshold': 0.12,      # 降低SVIP门槛
        'high_threshold': 0.06,      # 降低高潜力门槛
        'focus': 'recall'            # 注重覆盖度
    },
    
    # 如果目标是平衡精准度和覆盖度
    'balanced': {
        'svip_threshold': 0.15,      # 标准SVIP门槛
        'high_threshold': 0.08,      # 标准高潜力门槛
        'focus': 'balanced'          # 平衡策略
    }
}

# ==================== 季节性调整 ====================

# 不同时期的策略调整
SEASONAL_ADJUSTMENT = {
    'Q1': {  # 第一季度 - 新年新计划
        'strategy_focus': '职业规划和技能提升',
        'threshold_multiplier': 1.0,
        'special_attention': ['work_years=0-1年', 'work_status=待业']
    },
    
    'Q2': {  # 第二季度 - 职业发展期
        'strategy_focus': '职业晋升和能力提升',
        'threshold_multiplier': 1.1,
        'special_attention': ['work_years=3-5年', 'work_years=5-10年']
    },
    
    'Q3': {  # 第三季度 - 跳槽高峰期
        'strategy_focus': '职业转换和薪资提升',
        'threshold_multiplier': 0.9,
        'special_attention': ['work_status=已提交离职', 'work_status=待业']
    },
    
    'Q4': {  # 第四季度 - 年终总结期
        'strategy_focus': '年终总结和来年规划',
        'threshold_multiplier': 1.0,
        'special_attention': ['salary_range=25001-40000', 'salary_range=40000以上']
    }
}

# ==================== 使用说明 ====================

USAGE_INSTRUCTIONS = """
🔧 4维分类配置使用说明

1. 调整分类阈值:
   - 修改 SVIP_CONVERSION_THRESHOLD 来改变SVIP用户的转化率门槛
   - 修改 HIGH_POTENTIAL_CONVERSION_THRESHOLD 来改变高潜力用户的门槛
   - 修改最小样本量要求

2. 调整维度权重:
   - 修改 DIMENSION_WEIGHTS 中各维度的权重
   - 确保所有权重之和为1.0

3. 自定义营销策略:
   - 修改各级别用户的营销策略内容
   - 调整投入水平和预期ROI

4. 应用特殊规则:
   - 启用或修改 SPECIAL_RULES 中的特殊处理逻辑
   - 添加新的业务规则

5. 动态调整:
   - 根据业务目标选择 DYNAMIC_ADJUSTMENT 中的策略
   - 根据季节选择 SEASONAL_ADJUSTMENT 中的调整

6. 重新生成分类:
   - 修改配置后，运行 apply_custom_classification() 函数
   - 新的分类结果将基于您的配置生成

示例用法:
```python
from 4维分类配置 import *
from four_dimension_classifier import FourDimensionClassifier

# 应用自定义配置
classifier = FourDimensionClassifier("副本用户信息表.xlsx")
classifier.apply_custom_config(
    svip_threshold=SVIP_CONVERSION_THRESHOLD,
    high_threshold=HIGH_POTENTIAL_CONVERSION_THRESHOLD,
    dimension_weights=DIMENSION_WEIGHTS
)
```
"""

def apply_custom_classification(file_path="副本用户信息表.xlsx", config_mode="balanced"):
    """
    应用自定义配置重新生成分类
    
    Args:
        file_path: 数据文件路径
        config_mode: 配置模式 ('maximize_conversion', 'maximize_coverage', 'balanced')
    """
    from four_dimension_classifier import FourDimensionClassifier
    
    # 获取配置
    config = DYNAMIC_ADJUSTMENT.get(config_mode, DYNAMIC_ADJUSTMENT['balanced'])
    
    print(f"🔧 应用{config_mode}配置模式...")
    print(f"SVIP阈值: {config['svip_threshold']:.1%}")
    print(f"高潜力阈值: {config['high_threshold']:.1%}")
    
    # 创建分类器并应用配置
    classifier = FourDimensionClassifier(file_path)
    
    # 修改阈值
    classifier.svip_threshold = config['svip_threshold']
    classifier.high_threshold = config['high_threshold']
    
    # 运行分析
    success = classifier.run_complete_4d_analysis()
    
    if success:
        print("✅ 自定义配置分类完成!")
        print(f"📊 新的分类结果已保存")
        return classifier
    else:
        print("❌ 分类失败")
        return None

if __name__ == "__main__":
    print("📋 4维分类配置文件")
    print("="*50)
    print(USAGE_INSTRUCTIONS)
    
    # 示例：应用平衡配置
    # apply_custom_classification(config_mode="balanced")
