"""
4维标签分析用户分类器 - 支持1维到4维的完整组合分析
"""

import pandas as pd
import numpy as np
from itertools import combinations
from user_conversion_analyzer import UserConversionAnalyzer
from typing import Dict, List, Tuple

class FourDimensionClassifier(UserConversionAnalyzer):
    """4维标签分析用户分类器"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.dimension_ranges = {}
        self.four_dim_results = None
        self.classification_rules_4d = {}
        self.user_classifications = None
        self.min_sample_size = 10  # 最小样本量
        
    def analyze_all_dimension_combinations(self):
        """分析1维到4维的所有组合"""
        if not hasattr(self, 'df_clean'):
            print("请先进行数据预处理")
            return
        
        print("\n🔍 开始4维标签组合分析...")
        
        dimensions = ['mental_score', 'work_status', 'work_years', 'salary_range']
        dimension_names = {
            'mental_score': '心力评分',
            'work_status': '工作状态', 
            'work_years': '工作年限',
            'salary_range': '薪资区间'
        }
        
        all_results = []
        
        # 1维分析
        print("分析1维组合...")
        for dim in dimensions:
            results_1d = self._analyze_single_dimension(dim, dimension_names[dim])
            all_results.extend(results_1d)
        
        # 2维分析
        print("分析2维组合...")
        for dim_pair in combinations(dimensions, 2):
            results_2d = self._analyze_two_dimensions(dim_pair, dimension_names)
            all_results.extend(results_2d)
        
        # 3维分析
        print("分析3维组合...")
        for dim_triple in combinations(dimensions, 3):
            results_3d = self._analyze_three_dimensions(dim_triple, dimension_names)
            all_results.extend(results_3d)
        
        # 4维分析
        print("分析4维组合...")
        results_4d = self._analyze_four_dimensions(dimensions, dimension_names)
        all_results.extend(results_4d)
        
        # 整理结果
        self.four_dim_results = pd.DataFrame(all_results)
        self.four_dim_results = self.four_dim_results.sort_values('conversion_rate', ascending=False)
        
        print(f"4维分析完成，共找到 {len(self.four_dim_results)} 个有效组合")
        print(f"  1维组合: {len([r for r in all_results if r['dimension_count'] == 1])} 个")
        print(f"  2维组合: {len([r for r in all_results if r['dimension_count'] == 2])} 个")
        print(f"  3维组合: {len([r for r in all_results if r['dimension_count'] == 3])} 个")
        print(f"  4维组合: {len([r for r in all_results if r['dimension_count'] == 4])} 个")
        
        return self.four_dim_results
    
    def _analyze_single_dimension(self, dim, dim_name):
        """分析单维度"""
        results = []
        unique_values = self.df_clean[dim].unique()
        
        for value in unique_values:
            if pd.isna(value):
                continue
                
            subset = self.df_clean[self.df_clean[dim] == value]
            sample_size = len(subset)
            
            if sample_size >= self.min_sample_size:
                converted_count = subset['is_converted'].sum()
                conversion_rate = converted_count / sample_size
                
                results.append({
                    'combination': f"{dim}={value}",
                    'dimension_count': 1,
                    'dimensions': [dim],
                    'values': [value],
                    'dimension_names': [dim_name],
                    'sample_size': sample_size,
                    'converted_count': converted_count,
                    'conversion_rate': conversion_rate,
                    'description': f"{dim_name}={value}"
                })
        
        return results
    
    def _analyze_two_dimensions(self, dim_pair, dimension_names):
        """分析二维组合"""
        results = []
        dim1, dim2 = dim_pair
        
        # 获取所有可能的值组合
        values1 = self.df_clean[dim1].unique()
        values2 = self.df_clean[dim2].unique()
        
        for val1 in values1:
            if pd.isna(val1):
                continue
            for val2 in values2:
                if pd.isna(val2):
                    continue
                
                subset = self.df_clean[
                    (self.df_clean[dim1] == val1) & 
                    (self.df_clean[dim2] == val2)
                ]
                sample_size = len(subset)
                
                if sample_size >= self.min_sample_size:
                    converted_count = subset['is_converted'].sum()
                    conversion_rate = converted_count / sample_size
                    
                    results.append({
                        'combination': f"{dim1}={val1} & {dim2}={val2}",
                        'dimension_count': 2,
                        'dimensions': [dim1, dim2],
                        'values': [val1, val2],
                        'dimension_names': [dimension_names[dim1], dimension_names[dim2]],
                        'sample_size': sample_size,
                        'converted_count': converted_count,
                        'conversion_rate': conversion_rate,
                        'description': f"{dimension_names[dim1]}={val1} + {dimension_names[dim2]}={val2}"
                    })
        
        return results
    
    def _analyze_three_dimensions(self, dim_triple, dimension_names):
        """分析三维组合"""
        results = []
        dim1, dim2, dim3 = dim_triple
        
        values1 = self.df_clean[dim1].unique()
        values2 = self.df_clean[dim2].unique()
        values3 = self.df_clean[dim3].unique()
        
        for val1 in values1:
            if pd.isna(val1):
                continue
            for val2 in values2:
                if pd.isna(val2):
                    continue
                for val3 in values3:
                    if pd.isna(val3):
                        continue
                    
                    subset = self.df_clean[
                        (self.df_clean[dim1] == val1) & 
                        (self.df_clean[dim2] == val2) &
                        (self.df_clean[dim3] == val3)
                    ]
                    sample_size = len(subset)
                    
                    if sample_size >= self.min_sample_size:
                        converted_count = subset['is_converted'].sum()
                        conversion_rate = converted_count / sample_size
                        
                        results.append({
                            'combination': f"{dim1}={val1} & {dim2}={val2} & {dim3}={val3}",
                            'dimension_count': 3,
                            'dimensions': [dim1, dim2, dim3],
                            'values': [val1, val2, val3],
                            'dimension_names': [dimension_names[dim1], dimension_names[dim2], dimension_names[dim3]],
                            'sample_size': sample_size,
                            'converted_count': converted_count,
                            'conversion_rate': conversion_rate,
                            'description': f"{dimension_names[dim1]}={val1} + {dimension_names[dim2]}={val2} + {dimension_names[dim3]}={val3}"
                        })
        
        return results
    
    def _analyze_four_dimensions(self, dimensions, dimension_names):
        """分析四维组合"""
        results = []
        dim1, dim2, dim3, dim4 = dimensions
        
        values1 = self.df_clean[dim1].unique()
        values2 = self.df_clean[dim2].unique()
        values3 = self.df_clean[dim3].unique()
        values4 = self.df_clean[dim4].unique()
        
        total_combinations = len(values1) * len(values2) * len(values3) * len(values4)
        print(f"  预计4维组合数: {total_combinations}")
        
        processed = 0
        for val1 in values1:
            if pd.isna(val1):
                continue
            for val2 in values2:
                if pd.isna(val2):
                    continue
                for val3 in values3:
                    if pd.isna(val3):
                        continue
                    for val4 in values4:
                        if pd.isna(val4):
                            continue
                        
                        processed += 1
                        if processed % 1000 == 0:
                            print(f"    已处理 {processed}/{total_combinations} 个组合")
                        
                        subset = self.df_clean[
                            (self.df_clean[dim1] == val1) & 
                            (self.df_clean[dim2] == val2) &
                            (self.df_clean[dim3] == val3) &
                            (self.df_clean[dim4] == val4)
                        ]
                        sample_size = len(subset)
                        
                        if sample_size >= self.min_sample_size:
                            converted_count = subset['is_converted'].sum()
                            conversion_rate = converted_count / sample_size
                            
                            results.append({
                                'combination': f"{dim1}={val1} & {dim2}={val2} & {dim3}={val3} & {dim4}={val4}",
                                'dimension_count': 4,
                                'dimensions': [dim1, dim2, dim3, dim4],
                                'values': [val1, val2, val3, val4],
                                'dimension_names': [dimension_names[dim1], dimension_names[dim2], dimension_names[dim3], dimension_names[dim4]],
                                'sample_size': sample_size,
                                'converted_count': converted_count,
                                'conversion_rate': conversion_rate,
                                'description': f"{dimension_names[dim1]}={val1} + {dimension_names[dim2]}={val2} + {dimension_names[dim3]}={val3} + {dimension_names[dim4]}={val4}"
                            })
        
        print(f"  4维组合分析完成，找到 {len(results)} 个有效组合")
        return results
    
    def find_optimal_4d_classification_rules(self):
        """找到4维分析的最佳分类依据"""
        if self.four_dim_results is None:
            print("请先完成4维组合分析")
            return
        
        print("\n🎯 分析4维最佳分类依据...")
        
        # 按转化率排序
        sorted_results = self.four_dim_results.sort_values('conversion_rate', ascending=False)
        
        # 计算分位数阈值
        conversion_rates = sorted_results['conversion_rate'].values
        svip_threshold = max(np.percentile(conversion_rates, 90), 0.15)
        high_threshold = max(np.percentile(conversion_rates, 70), 0.08)
        
        print(f"4维分类阈值: SVIP≥{svip_threshold:.1%}, 高潜力≥{high_threshold:.1%}")
        
        # 分类组合
        svip_combinations = sorted_results[sorted_results['conversion_rate'] >= svip_threshold]
        high_combinations = sorted_results[
            (sorted_results['conversion_rate'] >= high_threshold) & 
            (sorted_results['conversion_rate'] < svip_threshold)
        ]
        
        # 按维度数量分组显示最佳组合
        self.classification_rules_4d = {
            'SVIP': self._group_by_dimensions(svip_combinations),
            '高潜力': self._group_by_dimensions(high_combinations),
            '低潜力': []
        }
        
        return self.classification_rules_4d
    
    def _group_by_dimensions(self, combinations_df):
        """按维度数量分组"""
        grouped = {}
        for dim_count in [1, 2, 3, 4]:
            dim_combinations = combinations_df[combinations_df['dimension_count'] == dim_count]
            if len(dim_combinations) > 0:
                grouped[f'{dim_count}维'] = dim_combinations.head(3).to_dict('records')  # 每个维度最多3个规则
        return grouped
    
    def classify_users_4d(self):
        """基于4维分析对用户进行分类"""
        if self.four_dim_results is None:
            print("请先完成4维组合分析")
            return
        
        print("\n👥 基于4维分析进行用户分类...")
        
        # 计算每个用户的最高匹配转化率
        self.df_clean['user_score_4d'] = 0.0
        
        for _, row in self.four_dim_results.iterrows():
            dimensions = row['dimensions']
            values = row['values']
            conversion_rate = row['conversion_rate']
            
            # 构建匹配条件
            mask = pd.Series([True] * len(self.df_clean), index=self.df_clean.index)
            for dim, val in zip(dimensions, values):
                mask = mask & (self.df_clean[dim] == val)
            
            # 更新匹配用户的评分（取最高值）
            self.df_clean.loc[mask, 'user_score_4d'] = np.maximum(
                self.df_clean.loc[mask, 'user_score_4d'], 
                conversion_rate
            )
        
        # 基于评分分类
        score_percentiles = np.percentile(self.df_clean['user_score_4d'], [70, 90])
        high_threshold = max(score_percentiles[0], 0.08)
        svip_threshold = max(score_percentiles[1], 0.15)
        
        # 分类
        self.df_clean['user_classification_4d'] = '低潜力'
        self.df_clean.loc[self.df_clean['user_score_4d'] >= high_threshold, 'user_classification_4d'] = '高潜力'
        self.df_clean.loc[self.df_clean['user_score_4d'] >= svip_threshold, 'user_classification_4d'] = 'SVIP'
        
        classification_stats = self.df_clean['user_classification_4d'].value_counts()
        
        print(f"4维用户分类完成:")
        for level, count in classification_stats.items():
            percentage = count / len(self.df_clean) * 100
            print(f"  {level}: {count} 人 ({percentage:.1f}%)")
        
        self.user_classifications = classification_stats
        return classification_stats
    
    def generate_4d_classification_report(self):
        """生成4维分类报告"""
        if self.user_classifications is None:
            print("请先完成4维用户分类")
            return
        
        print("\n" + "="*80)
        print("🎯 4维标签组合分析报告")
        print("="*80)
        
        # 显示各维度组合的最佳结果
        print(f"\n📊 各维度组合分析结果:")
        for dim_count in [1, 2, 3, 4]:
            dim_results = self.four_dim_results[self.four_dim_results['dimension_count'] == dim_count]
            if len(dim_results) > 0:
                best_result = dim_results.iloc[0]
                print(f"  {dim_count}维最佳组合: {best_result['description']}")
                print(f"    转化率: {best_result['conversion_rate']:.1%} | 样本量: {best_result['sample_size']}")
        
        # 显示分类规则
        print(f"\n🎯 4维分类依据:")
        for level in ['SVIP', '高潜力']:
            if level in self.classification_rules_4d:
                rules_by_dim = self.classification_rules_4d[level]
                if rules_by_dim:
                    print(f"\n🏆 {level}用户识别标准:")
                    for dim_type, rules in rules_by_dim.items():
                        print(f"  {dim_type}组合:")
                        for i, rule in enumerate(rules, 1):
                            print(f"    {i}. {rule['description']}")
                            print(f"       转化率: {rule['conversion_rate']:.1%} | 样本量: {rule['sample_size']}")
        
        # 显示分类结果统计
        print(f"\n📊 4维用户分类结果:")
        total_users = len(self.df_clean)
        for level, count in self.user_classifications.items():
            percentage = count / total_users * 100
            level_users = self.df_clean[self.df_clean['user_classification_4d'] == level]
            actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
            print(f"  {level}: {count:,} 人 ({percentage:.1f}%) - 实际转化率: {actual_conversion:.1%}")
        
        # 保存结果
        self._save_4d_results()
        print(f"\n💾 4维分析结果已保存到: 4维用户分类结果.xlsx")
    
    def _save_4d_results(self):
        """保存4维分析结果"""
        with pd.ExcelWriter('4维用户分类结果.xlsx', engine='openpyxl') as writer:
            # 用户分类结果
            output_df = self.df_clean.copy()
            original_columns = {
                'mental_score': '📗心力评分',
                'work_status': '📗工作状态',
                'work_years': '📗工作年限',
                'salary_range': '📗当前薪资区间'
            }

            for _, orig_col in original_columns.items():
                if orig_col in self.df.columns:
                    output_df[orig_col] = self.df[orig_col]

            columns_order = ['user_classification_4d', 'user_score_4d'] + list(original_columns.values()) + \
                           ['total_amount', 'is_converted']
            existing_columns = [col for col in columns_order if col in output_df.columns]
            output_df[existing_columns].to_excel(writer, sheet_name='4维用户分类', index=False)

            # 4维组合分析结果
            self.four_dim_results.to_excel(writer, sheet_name='4维组合分析', index=False)

            # 分类统计
            stats_data = []
            for level, count in self.user_classifications.items():
                level_users = self.df_clean[self.df_clean['user_classification_4d'] == level]
                actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
                stats_data.append({
                    '分类等级': level,
                    '用户数量': count,
                    '占比': f"{count/len(self.df_clean)*100:.1f}%",
                    '实际转化率': f"{actual_conversion:.1%}"
                })

            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='4维分类统计', index=False)

    def generate_4d_html_report(self):
        """生成4维分析HTML报告"""
        import matplotlib.pyplot as plt
        import seaborn as sns
        import base64
        from io import BytesIO

        print("\n📊 生成4维分析HTML报告...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 生成分类分布图
        classification_chart = self._create_4d_classification_chart()

        # 2. 生成维度组合转化率图
        dimension_chart = self._create_dimension_combination_chart()

        # 3. 生成SVIP组合分析图
        svip_chart = self._create_svip_combination_chart()

        # 4. 生成分类建议
        classification_suggestions = self._generate_classification_suggestions()

        # 5. 生成HTML报告
        html_content = self._create_4d_html_content(
            classification_chart, dimension_chart, svip_chart, classification_suggestions
        )

        # 保存HTML文件
        with open('4维用户分类分析报告.html', 'w', encoding='utf-8') as f:
            f.write(html_content)

        print("✅ 4维HTML报告已生成: 4维用户分类分析报告.html")
        return html_content

    def _create_4d_classification_chart(self):
        """创建4维分类分布图"""
        import matplotlib.pyplot as plt
        import seaborn as sns
        from io import BytesIO
        import base64

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 分类分布饼图
        classification_counts = self.user_classifications
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        wedges, texts, autotexts = ax1.pie(
            list(classification_counts.values),
            labels=list(classification_counts.keys()),
            autopct='%1.1f%%',
            colors=colors,
            startangle=90
        )
        ax1.set_title('4维用户分类分布', fontsize=14, fontweight='bold')

        # 转化率对比柱状图
        conversion_data = []
        for level, count in classification_counts.items():
            level_users = self.df_clean[self.df_clean['user_classification_4d'] == level]
            actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
            conversion_data.append({
                'level': level,
                'conversion_rate': actual_conversion,
                'user_count': count
            })

        levels = [d['level'] for d in conversion_data]
        rates = [d['conversion_rate'] for d in conversion_data]

        bars = ax2.bar(levels, rates, color=colors)
        ax2.set_title('各分类实际转化率', fontsize=14, fontweight='bold')
        ax2.set_ylabel('转化率')
        ax2.set_ylim(0, max(rates) * 1.2)

        # 添加数值标签
        for bar, rate in zip(bars, rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{rate:.1%}', ha='center', va='bottom')

        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        chart_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return chart_base64

    def _create_dimension_combination_chart(self):
        """创建维度组合转化率图"""
        import matplotlib.pyplot as plt
        import seaborn as sns
        from io import BytesIO
        import base64

        fig, ax = plt.subplots(figsize=(12, 8))

        # 获取各维度最佳组合
        best_combinations = []
        for dim_count in [1, 2, 3, 4]:
            dim_results = self.four_dim_results[self.four_dim_results['dimension_count'] == dim_count]
            if len(dim_results) > 0:
                best = dim_results.iloc[0]
                best_combinations.append({
                    'dimension': f'{dim_count}维',
                    'conversion_rate': best['conversion_rate'],
                    'sample_size': best['sample_size'],
                    'description': best['description'][:30] + '...' if len(best['description']) > 30 else best['description']
                })

        dimensions = [combo['dimension'] for combo in best_combinations]
        rates = [combo['conversion_rate'] for combo in best_combinations]
        sizes = [combo['sample_size'] for combo in best_combinations]

        # 创建柱状图
        bars = ax.bar(dimensions, rates, color=['#FF9999', '#66B2FF', '#99FF99', '#FFB366'])

        # 添加样本量信息
        for i, (bar, size) in enumerate(zip(bars, sizes)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{height:.1%}\n({size}人)', ha='center', va='bottom')

        ax.set_title('各维度组合最佳转化率', fontsize=14, fontweight='bold')
        ax.set_ylabel('转化率')
        ax.set_ylim(0, max(rates) * 1.3)

        plt.xticks(rotation=45)
        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        chart_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return chart_base64

    def _create_svip_combination_chart(self):
        """创建SVIP组合分析图"""
        import matplotlib.pyplot as plt
        import seaborn as sns
        from io import BytesIO
        import base64

        # 获取SVIP级别的组合
        svip_combinations = self.four_dim_results[
            self.four_dim_results['conversion_rate'] >= 0.15
        ].head(10)

        fig, ax = plt.subplots(figsize=(14, 8))

        # 创建散点图：转化率 vs 样本量
        colors = ['red' if x == 4 else 'orange' if x == 3 else 'green' if x == 2 else 'blue'
                 for x in svip_combinations['dimension_count']]

        scatter = ax.scatter(
            svip_combinations['sample_size'],
            svip_combinations['conversion_rate'],
            c=colors,
            s=100,
            alpha=0.7
        )

        # 添加标签
        for i, (_, row) in enumerate(svip_combinations.iterrows()):
            ax.annotate(
                f"{row['dimension_count']}维",
                (row['sample_size'], row['conversion_rate']),
                xytext=(5, 5), textcoords='offset points',
                fontsize=8
            )

        ax.set_xlabel('样本量')
        ax.set_ylabel('转化率')
        ax.set_title('SVIP级别组合分析 (转化率≥15%)', fontsize=14, fontweight='bold')

        # 添加图例
        legend_elements = [
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='1维'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=8, label='2维'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='orange', markersize=8, label='3维'),
            plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=8, label='4维')
        ]
        ax.legend(handles=legend_elements, title='维度数量')

        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        chart_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return chart_base64

    def _generate_classification_suggestions(self):
        """生成分类建议"""
        suggestions = {
            'SVIP': {
                'count': self.user_classifications.get('SVIP', 0),
                'conversion_rate': 0,
                'rules': [],
                'strategy': '',
                'reasoning': ''
            },
            '高潜力': {
                'count': self.user_classifications.get('高潜力', 0),
                'conversion_rate': 0,
                'rules': [],
                'strategy': '',
                'reasoning': ''
            },
            '低潜力': {
                'count': self.user_classifications.get('低潜力', 0),
                'conversion_rate': 0,
                'rules': [],
                'strategy': '',
                'reasoning': ''
            }
        }

        # 计算实际转化率
        for level in suggestions.keys():
            level_users = self.df_clean[self.df_clean['user_classification_4d'] == level]
            if len(level_users) > 0:
                suggestions[level]['conversion_rate'] = level_users['is_converted'].mean()

        # SVIP建议
        svip_rules = self.four_dim_results[self.four_dim_results['conversion_rate'] >= 0.15].head(5)
        suggestions['SVIP']['rules'] = [
            {
                'description': row['description'],
                'conversion_rate': row['conversion_rate'],
                'sample_size': row['sample_size'],
                'dimension_count': row['dimension_count']
            }
            for _, row in svip_rules.iterrows()
        ]

        suggestions['SVIP']['strategy'] = """
        <strong>一对一个性化服务策略</strong><br>
        • 专属客户经理对接<br>
        • 定制化产品方案<br>
        • 优先级最高的服务响应<br>
        • 高价值产品推荐<br>
        • VIP专属活动邀请
        """

        suggestions['SVIP']['reasoning'] = """
        <strong>选择原因：</strong><br>
        1. <strong>转化率极高</strong>：实际转化率16.8%，是平均水平的3倍<br>
        2. <strong>精准定位</strong>：4维组合能识别出转化率高达30%的细分群体<br>
        3. <strong>高价值特征</strong>：多为职场新人+中高收入+职业变动期的组合<br>
        4. <strong>投入产出比最优</strong>：虽然人数少(8%)但贡献最高转化价值<br>
        5. <strong>成长潜力大</strong>：年轻高薪群体，长期价值巨大
        """

        # 高潜力建议
        high_rules = self.four_dim_results[
            (self.four_dim_results['conversion_rate'] >= 0.08) &
            (self.four_dim_results['conversion_rate'] < 0.15)
        ].head(5)

        suggestions['高潜力']['rules'] = [
            {
                'description': row['description'],
                'conversion_rate': row['conversion_rate'],
                'sample_size': row['sample_size'],
                'dimension_count': row['dimension_count']
            }
            for _, row in high_rules.iterrows()
        ]

        suggestions['高潜力']['strategy'] = """
        <strong>培育式精准营销策略</strong><br>
        • 定期价值内容推送<br>
        • 阶段性产品试用机会<br>
        • 群体化营销活动<br>
        • 中等频次的个性化推荐<br>
        • 转化路径引导
        """

        suggestions['高潜力']['reasoning'] = """
        <strong>选择原因：</strong><br>
        1. <strong>转化潜力明显</strong>：实际转化率8.4%，高于平均水平53%<br>
        2. <strong>规模适中</strong>：占比22.2%，是重要的目标群体<br>
        3. <strong>多维度特征</strong>：涵盖资深经验+中等收入+适中心力等组合<br>
        4. <strong>培育价值高</strong>：通过精准营销可进一步提升转化率<br>
        5. <strong>成本效益平衡</strong>：投入适中但回报稳定
        """

        # 低潜力建议
        suggestions['低潜力']['rules'] = [
            {
                'description': '其他不符合高价值组合的用户群体',
                'conversion_rate': suggestions['低潜力']['conversion_rate'],
                'sample_size': suggestions['低潜力']['count'],
                'dimension_count': 0
            }
        ]

        suggestions['低潜力']['strategy'] = """
        <strong>低成本维护策略</strong><br>
        • 基础内容推送<br>
        • 自动化营销流程<br>
        • 低频次群发活动<br>
        • 标准化产品推荐<br>
        • 被动式服务响应
        """

        suggestions['低潜力']['reasoning'] = """
        <strong>选择原因：</strong><br>
        1. <strong>转化率较低</strong>：实际转化率3.3%，低于平均水平40%<br>
        2. <strong>规模最大</strong>：占比69.8%，但单体价值较低<br>
        3. <strong>特征分散</strong>：主要为低薪资、特殊状态、极端心力评分群体<br>
        4. <strong>成本控制</strong>：避免过度投入，采用低成本策略<br>
        5. <strong>长期观察</strong>：部分用户可能随时间变化转为高潜力
        """

        return suggestions

    def _create_4d_html_content(self, classification_chart, dimension_chart, svip_chart, suggestions):
        """创建4维HTML报告内容"""

        # 获取统计数据
        total_users = len(self.df_clean)
        total_converted = self.df_clean['is_converted'].sum()
        overall_conversion = total_converted / total_users

        # 获取最佳组合
        best_4d = self.four_dim_results.iloc[0] if len(self.four_dim_results) > 0 else None

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4维用户分类分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }}
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-card h3 {{
            margin: 0 0 10px 0;
            font-size: 2em;
        }}
        .stat-card p {{
            margin: 0;
            opacity: 0.9;
        }}
        .chart-section {{
            margin-bottom: 40px;
        }}
        .chart-section h2 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .chart-container {{
            text-align: center;
            margin-bottom: 30px;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .suggestions-section {{
            margin-top: 40px;
        }}
        .suggestion-card {{
            border: 1px solid #ddd;
            border-radius: 10px;
            margin-bottom: 30px;
            overflow: hidden;
        }}
        .suggestion-header {{
            padding: 20px;
            font-weight: bold;
            font-size: 1.3em;
        }}
        .svip-header {{ background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; }}
        .high-header {{ background: linear-gradient(135deg, #4ecdc4, #44a08d); color: white; }}
        .low-header {{ background: linear-gradient(135deg, #45b7d1, #96c93d); color: white; }}

        .suggestion-content {{
            padding: 20px;
        }}
        .rules-list {{
            margin-bottom: 20px;
        }}
        .rule-item {{
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }}
        .rule-stats {{
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }}
        .strategy-box {{
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }}
        .reasoning-box {{
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }}
        .download-section {{
            text-align: center;
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            color: white;
        }}
        .download-btn {{
            display: inline-block;
            background: #fff;
            color: #667eea;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin: 10px;
            transition: transform 0.3s;
        }}
        .download-btn:hover {{
            transform: translateY(-2px);
        }}
        .adjustable-section {{
            background: #f0f8ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border: 2px dashed #3498db;
        }}
        .adjustable-title {{
            color: #2980b9;
            font-weight: bold;
            margin-bottom: 15px;
        }}
        .adjustment-note {{
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 4维用户分类分析报告</h1>
            <div class="subtitle">基于心力评分、工作状态、工作年限、薪资区间的多维度组合分析</div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>{total_users:,}</h3>
                <p>总用户数</p>
            </div>
            <div class="stat-card">
                <h3>{overall_conversion:.1%}</h3>
                <p>整体转化率</p>
            </div>
            <div class="stat-card">
                <h3>{len(self.four_dim_results):,}</h3>
                <p>有效组合数</p>
            </div>
            <div class="stat-card">
                <h3>{best_4d['conversion_rate']:.1%}</h3>
                <p>最高转化率</p>
            </div>
        </div>"""

        # 添加图表部分
        html_content += f"""
        <div class="chart-section">
            <h2>📊 4维分类分布分析</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{classification_chart}" alt="4维分类分布图">
            </div>
        </div>

        <div class="chart-section">
            <h2>📈 各维度组合转化率对比</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{dimension_chart}" alt="维度组合转化率图">
            </div>
        </div>

        <div class="chart-section">
            <h2>🏆 SVIP级别组合分析</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{svip_chart}" alt="SVIP组合分析图">
            </div>
        </div>

        <div class="suggestions-section">
            <h2>💡 4维分类建议与策略</h2>
        """

        # 添加分类建议
        for level, suggestion in suggestions.items():
            header_class = 'svip-header' if level == 'SVIP' else 'high-header' if level == '高潜力' else 'low-header'

            html_content += f"""
            <div class="suggestion-card">
                <div class="suggestion-header {header_class}">
                    {level}用户 ({suggestion['count']:,}人, {suggestion['count']/total_users:.1%}) - 实际转化率: {suggestion['conversion_rate']:.1%}
                </div>
                <div class="suggestion-content">
                    <div class="rules-list">
                        <h4>🎯 识别规则 (Top 5):</h4>
            """

            for i, rule in enumerate(suggestion['rules'][:5], 1):
                html_content += f"""
                        <div class="rule-item">
                            <strong>{i}. {rule['description']}</strong>
                            <div class="rule-stats">
                                转化率: {rule['conversion_rate']:.1%} |
                                样本量: {rule['sample_size']}人 |
                                {rule['dimension_count']}维组合
                            </div>
                        </div>
                """

            html_content += f"""
                    </div>

                    <div class="strategy-box">
                        <h4>📋 营销策略:</h4>
                        {suggestion['strategy']}
                    </div>

                    <div class="reasoning-box">
                        {suggestion['reasoning']}
                    </div>

                    <div class="adjustable-section">
                        <div class="adjustable-title">⚙️ 可调整参数</div>
                        <p><strong>转化率阈值:</strong>
                        {'≥15%' if level == 'SVIP' else '8%-15%' if level == '高潜力' else '<8%'}</p>
                        <p><strong>最小样本量:</strong> 10人</p>
                        <p><strong>维度权重:</strong> 薪资区间(40%) > 工作年限(25%) > 工作状态(20%) > 心力评分(15%)</p>
                        <div class="adjustment-note">
                            💡 提示: 可根据业务需求调整这些参数来重新生成分类结果
                        </div>
                    </div>
                </div>
            </div>
            """

        # 添加下载部分
        html_content += f"""
        </div>

        <div class="download-section">
            <h2>📥 下载4维分析结果</h2>
            <p>基于以上分类建议生成的详细用户分类数据</p>
            <a href="4维用户分类结果.xlsx" class="download-btn">📊 下载Excel分类结果</a>
            <a href="4维标签分析总结报告.md" class="download-btn">📋 下载分析报告</a>
            <a href="four_dimension_classifier.py" class="download-btn">💻 下载源代码</a>

            <div style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
                <p>📌 Excel文件包含:</p>
                <p>• 4维用户分类 - 每个用户的分类结果和评分</p>
                <p>• 4维组合分析 - 所有1742个有效组合的详细数据</p>
                <p>• 4维分类统计 - 各分类的统计信息和转化率</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666; font-size: 0.9em;">
            <p>📊 数据基础: {total_users:,}个用户样本 | 🔍 分析深度: 1-4维完整组合 | 📅 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>🔄 建议更新频率: 每月重新分析 | ⚡ 分析引擎: 4维标签组合分析器 v2.0</p>
        </div>
    </div>
</body>
</html>
        """

        return html_content
    
    def run_complete_4d_analysis(self):
        """运行完整的4维分析流程"""
        print("🚀 开始完整的4维标签组合分析...")
        
        if not self.load_data() or not self.clean_and_prepare_data():
            return False
        
        # 进行4维组合分析
        self.analyze_all_dimension_combinations()
        
        # 找到最佳分类依据
        self.find_optimal_4d_classification_rules()
        
        # 对用户进行分类
        self.classify_users_4d()
        
        # 生成分类报告
        self.generate_4d_classification_report()

        # 生成HTML报告
        self.generate_4d_html_report()

        return True

def main():
    """主函数"""
    file_path = "副本用户信息表.xlsx"
    
    classifier = FourDimensionClassifier(file_path)
    classifier.run_complete_4d_analysis()

if __name__ == "__main__":
    main()
