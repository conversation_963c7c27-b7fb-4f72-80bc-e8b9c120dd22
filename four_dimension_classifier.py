"""
4维标签分析用户分类器 - 支持1维到4维的完整组合分析
"""

import pandas as pd
import numpy as np
from itertools import combinations
from user_conversion_analyzer import UserConversionAnalyzer
from typing import Dict, List, Tuple

class FourDimensionClassifier(UserConversionAnalyzer):
    """4维标签分析用户分类器"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.dimension_ranges = {}
        self.four_dim_results = None
        self.classification_rules_4d = {}
        self.user_classifications = None
        self.min_sample_size = 10  # 最小样本量
        
    def analyze_all_dimension_combinations(self):
        """分析1维到4维的所有组合"""
        if not hasattr(self, 'df_clean'):
            print("请先进行数据预处理")
            return
        
        print("\n🔍 开始4维标签组合分析...")
        
        dimensions = ['mental_score', 'work_status', 'work_years', 'salary_range']
        dimension_names = {
            'mental_score': '心力评分',
            'work_status': '工作状态', 
            'work_years': '工作年限',
            'salary_range': '薪资区间'
        }
        
        all_results = []
        
        # 1维分析
        print("分析1维组合...")
        for dim in dimensions:
            results_1d = self._analyze_single_dimension(dim, dimension_names[dim])
            all_results.extend(results_1d)
        
        # 2维分析
        print("分析2维组合...")
        for dim_pair in combinations(dimensions, 2):
            results_2d = self._analyze_two_dimensions(dim_pair, dimension_names)
            all_results.extend(results_2d)
        
        # 3维分析
        print("分析3维组合...")
        for dim_triple in combinations(dimensions, 3):
            results_3d = self._analyze_three_dimensions(dim_triple, dimension_names)
            all_results.extend(results_3d)
        
        # 4维分析
        print("分析4维组合...")
        results_4d = self._analyze_four_dimensions(dimensions, dimension_names)
        all_results.extend(results_4d)
        
        # 整理结果
        self.four_dim_results = pd.DataFrame(all_results)
        self.four_dim_results = self.four_dim_results.sort_values('conversion_rate', ascending=False)
        
        print(f"4维分析完成，共找到 {len(self.four_dim_results)} 个有效组合")
        print(f"  1维组合: {len([r for r in all_results if r['dimension_count'] == 1])} 个")
        print(f"  2维组合: {len([r for r in all_results if r['dimension_count'] == 2])} 个")
        print(f"  3维组合: {len([r for r in all_results if r['dimension_count'] == 3])} 个")
        print(f"  4维组合: {len([r for r in all_results if r['dimension_count'] == 4])} 个")
        
        return self.four_dim_results
    
    def _analyze_single_dimension(self, dim, dim_name):
        """分析单维度"""
        results = []
        unique_values = self.df_clean[dim].unique()
        
        for value in unique_values:
            if pd.isna(value):
                continue
                
            subset = self.df_clean[self.df_clean[dim] == value]
            sample_size = len(subset)
            
            if sample_size >= self.min_sample_size:
                converted_count = subset['is_converted'].sum()
                conversion_rate = converted_count / sample_size
                
                results.append({
                    'combination': f"{dim}={value}",
                    'dimension_count': 1,
                    'dimensions': [dim],
                    'values': [value],
                    'dimension_names': [dim_name],
                    'sample_size': sample_size,
                    'converted_count': converted_count,
                    'conversion_rate': conversion_rate,
                    'description': f"{dim_name}={value}"
                })
        
        return results
    
    def _analyze_two_dimensions(self, dim_pair, dimension_names):
        """分析二维组合"""
        results = []
        dim1, dim2 = dim_pair
        
        # 获取所有可能的值组合
        values1 = self.df_clean[dim1].unique()
        values2 = self.df_clean[dim2].unique()
        
        for val1 in values1:
            if pd.isna(val1):
                continue
            for val2 in values2:
                if pd.isna(val2):
                    continue
                
                subset = self.df_clean[
                    (self.df_clean[dim1] == val1) & 
                    (self.df_clean[dim2] == val2)
                ]
                sample_size = len(subset)
                
                if sample_size >= self.min_sample_size:
                    converted_count = subset['is_converted'].sum()
                    conversion_rate = converted_count / sample_size
                    
                    results.append({
                        'combination': f"{dim1}={val1} & {dim2}={val2}",
                        'dimension_count': 2,
                        'dimensions': [dim1, dim2],
                        'values': [val1, val2],
                        'dimension_names': [dimension_names[dim1], dimension_names[dim2]],
                        'sample_size': sample_size,
                        'converted_count': converted_count,
                        'conversion_rate': conversion_rate,
                        'description': f"{dimension_names[dim1]}={val1} + {dimension_names[dim2]}={val2}"
                    })
        
        return results
    
    def _analyze_three_dimensions(self, dim_triple, dimension_names):
        """分析三维组合"""
        results = []
        dim1, dim2, dim3 = dim_triple
        
        values1 = self.df_clean[dim1].unique()
        values2 = self.df_clean[dim2].unique()
        values3 = self.df_clean[dim3].unique()
        
        for val1 in values1:
            if pd.isna(val1):
                continue
            for val2 in values2:
                if pd.isna(val2):
                    continue
                for val3 in values3:
                    if pd.isna(val3):
                        continue
                    
                    subset = self.df_clean[
                        (self.df_clean[dim1] == val1) & 
                        (self.df_clean[dim2] == val2) &
                        (self.df_clean[dim3] == val3)
                    ]
                    sample_size = len(subset)
                    
                    if sample_size >= self.min_sample_size:
                        converted_count = subset['is_converted'].sum()
                        conversion_rate = converted_count / sample_size
                        
                        results.append({
                            'combination': f"{dim1}={val1} & {dim2}={val2} & {dim3}={val3}",
                            'dimension_count': 3,
                            'dimensions': [dim1, dim2, dim3],
                            'values': [val1, val2, val3],
                            'dimension_names': [dimension_names[dim1], dimension_names[dim2], dimension_names[dim3]],
                            'sample_size': sample_size,
                            'converted_count': converted_count,
                            'conversion_rate': conversion_rate,
                            'description': f"{dimension_names[dim1]}={val1} + {dimension_names[dim2]}={val2} + {dimension_names[dim3]}={val3}"
                        })
        
        return results
    
    def _analyze_four_dimensions(self, dimensions, dimension_names):
        """分析四维组合"""
        results = []
        dim1, dim2, dim3, dim4 = dimensions
        
        values1 = self.df_clean[dim1].unique()
        values2 = self.df_clean[dim2].unique()
        values3 = self.df_clean[dim3].unique()
        values4 = self.df_clean[dim4].unique()
        
        total_combinations = len(values1) * len(values2) * len(values3) * len(values4)
        print(f"  预计4维组合数: {total_combinations}")
        
        processed = 0
        for val1 in values1:
            if pd.isna(val1):
                continue
            for val2 in values2:
                if pd.isna(val2):
                    continue
                for val3 in values3:
                    if pd.isna(val3):
                        continue
                    for val4 in values4:
                        if pd.isna(val4):
                            continue
                        
                        processed += 1
                        if processed % 1000 == 0:
                            print(f"    已处理 {processed}/{total_combinations} 个组合")
                        
                        subset = self.df_clean[
                            (self.df_clean[dim1] == val1) & 
                            (self.df_clean[dim2] == val2) &
                            (self.df_clean[dim3] == val3) &
                            (self.df_clean[dim4] == val4)
                        ]
                        sample_size = len(subset)
                        
                        if sample_size >= self.min_sample_size:
                            converted_count = subset['is_converted'].sum()
                            conversion_rate = converted_count / sample_size
                            
                            results.append({
                                'combination': f"{dim1}={val1} & {dim2}={val2} & {dim3}={val3} & {dim4}={val4}",
                                'dimension_count': 4,
                                'dimensions': [dim1, dim2, dim3, dim4],
                                'values': [val1, val2, val3, val4],
                                'dimension_names': [dimension_names[dim1], dimension_names[dim2], dimension_names[dim3], dimension_names[dim4]],
                                'sample_size': sample_size,
                                'converted_count': converted_count,
                                'conversion_rate': conversion_rate,
                                'description': f"{dimension_names[dim1]}={val1} + {dimension_names[dim2]}={val2} + {dimension_names[dim3]}={val3} + {dimension_names[dim4]}={val4}"
                            })
        
        print(f"  4维组合分析完成，找到 {len(results)} 个有效组合")
        return results
    
    def find_optimal_4d_classification_rules(self):
        """找到4维分析的最佳分类依据"""
        if self.four_dim_results is None:
            print("请先完成4维组合分析")
            return
        
        print("\n🎯 分析4维最佳分类依据...")
        
        # 按转化率排序
        sorted_results = self.four_dim_results.sort_values('conversion_rate', ascending=False)
        
        # 计算分位数阈值
        conversion_rates = sorted_results['conversion_rate'].values
        svip_threshold = max(np.percentile(conversion_rates, 90), 0.15)
        high_threshold = max(np.percentile(conversion_rates, 70), 0.08)
        
        print(f"4维分类阈值: SVIP≥{svip_threshold:.1%}, 高潜力≥{high_threshold:.1%}")
        
        # 分类组合
        svip_combinations = sorted_results[sorted_results['conversion_rate'] >= svip_threshold]
        high_combinations = sorted_results[
            (sorted_results['conversion_rate'] >= high_threshold) & 
            (sorted_results['conversion_rate'] < svip_threshold)
        ]
        
        # 按维度数量分组显示最佳组合
        self.classification_rules_4d = {
            'SVIP': self._group_by_dimensions(svip_combinations),
            '高潜力': self._group_by_dimensions(high_combinations),
            '低潜力': []
        }
        
        return self.classification_rules_4d
    
    def _group_by_dimensions(self, combinations_df):
        """按维度数量分组"""
        grouped = {}
        for dim_count in [1, 2, 3, 4]:
            dim_combinations = combinations_df[combinations_df['dimension_count'] == dim_count]
            if len(dim_combinations) > 0:
                grouped[f'{dim_count}维'] = dim_combinations.head(3).to_dict('records')  # 每个维度最多3个规则
        return grouped
    
    def classify_users_4d(self):
        """基于4维分析对用户进行分类"""
        if self.four_dim_results is None:
            print("请先完成4维组合分析")
            return
        
        print("\n👥 基于4维分析进行用户分类...")
        
        # 计算每个用户的最高匹配转化率
        self.df_clean['user_score_4d'] = 0.0
        
        for _, row in self.four_dim_results.iterrows():
            dimensions = row['dimensions']
            values = row['values']
            conversion_rate = row['conversion_rate']
            
            # 构建匹配条件
            mask = pd.Series([True] * len(self.df_clean), index=self.df_clean.index)
            for dim, val in zip(dimensions, values):
                mask = mask & (self.df_clean[dim] == val)
            
            # 更新匹配用户的评分（取最高值）
            self.df_clean.loc[mask, 'user_score_4d'] = np.maximum(
                self.df_clean.loc[mask, 'user_score_4d'], 
                conversion_rate
            )
        
        # 基于评分分类
        score_percentiles = np.percentile(self.df_clean['user_score_4d'], [70, 90])
        high_threshold = max(score_percentiles[0], 0.08)
        svip_threshold = max(score_percentiles[1], 0.15)
        
        # 分类
        self.df_clean['user_classification_4d'] = '低潜力'
        self.df_clean.loc[self.df_clean['user_score_4d'] >= high_threshold, 'user_classification_4d'] = '高潜力'
        self.df_clean.loc[self.df_clean['user_score_4d'] >= svip_threshold, 'user_classification_4d'] = 'SVIP'
        
        classification_stats = self.df_clean['user_classification_4d'].value_counts()
        
        print(f"4维用户分类完成:")
        for level, count in classification_stats.items():
            percentage = count / len(self.df_clean) * 100
            print(f"  {level}: {count} 人 ({percentage:.1f}%)")
        
        self.user_classifications = classification_stats
        return classification_stats
    
    def generate_4d_classification_report(self):
        """生成4维分类报告"""
        if self.user_classifications is None:
            print("请先完成4维用户分类")
            return
        
        print("\n" + "="*80)
        print("🎯 4维标签组合分析报告")
        print("="*80)
        
        # 显示各维度组合的最佳结果
        print(f"\n📊 各维度组合分析结果:")
        for dim_count in [1, 2, 3, 4]:
            dim_results = self.four_dim_results[self.four_dim_results['dimension_count'] == dim_count]
            if len(dim_results) > 0:
                best_result = dim_results.iloc[0]
                print(f"  {dim_count}维最佳组合: {best_result['description']}")
                print(f"    转化率: {best_result['conversion_rate']:.1%} | 样本量: {best_result['sample_size']}")
        
        # 显示分类规则
        print(f"\n🎯 4维分类依据:")
        for level in ['SVIP', '高潜力']:
            if level in self.classification_rules_4d:
                rules_by_dim = self.classification_rules_4d[level]
                if rules_by_dim:
                    print(f"\n🏆 {level}用户识别标准:")
                    for dim_type, rules in rules_by_dim.items():
                        print(f"  {dim_type}组合:")
                        for i, rule in enumerate(rules, 1):
                            print(f"    {i}. {rule['description']}")
                            print(f"       转化率: {rule['conversion_rate']:.1%} | 样本量: {rule['sample_size']}")
        
        # 显示分类结果统计
        print(f"\n📊 4维用户分类结果:")
        total_users = len(self.df_clean)
        for level, count in self.user_classifications.items():
            percentage = count / total_users * 100
            level_users = self.df_clean[self.df_clean['user_classification_4d'] == level]
            actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
            print(f"  {level}: {count:,} 人 ({percentage:.1f}%) - 实际转化率: {actual_conversion:.1%}")
        
        # 保存结果
        self._save_4d_results()
        print(f"\n💾 4维分析结果已保存到: 4维用户分类结果.xlsx")
    
    def _save_4d_results(self):
        """保存4维分析结果"""
        with pd.ExcelWriter('4维用户分类结果.xlsx', engine='openpyxl') as writer:
            # 用户分类结果
            output_df = self.df_clean.copy()
            original_columns = {
                'mental_score': '📗心力评分',
                'work_status': '📗工作状态',
                'work_years': '📗工作年限',
                'salary_range': '📗当前薪资区间'
            }
            
            for _, orig_col in original_columns.items():
                if orig_col in self.df.columns:
                    output_df[orig_col] = self.df[orig_col]
            
            columns_order = ['user_classification_4d', 'user_score_4d'] + list(original_columns.values()) + \
                           ['total_amount', 'is_converted']
            existing_columns = [col for col in columns_order if col in output_df.columns]
            output_df[existing_columns].to_excel(writer, sheet_name='4维用户分类', index=False)
            
            # 4维组合分析结果
            self.four_dim_results.to_excel(writer, sheet_name='4维组合分析', index=False)
            
            # 分类统计
            stats_data = []
            for level, count in self.user_classifications.items():
                level_users = self.df_clean[self.df_clean['user_classification_4d'] == level]
                actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
                stats_data.append({
                    '分类等级': level,
                    '用户数量': count,
                    '占比': f"{count/len(self.df_clean)*100:.1f}%",
                    '实际转化率': f"{actual_conversion:.1%}"
                })
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='4维分类统计', index=False)
    
    def run_complete_4d_analysis(self):
        """运行完整的4维分析流程"""
        print("🚀 开始完整的4维标签组合分析...")
        
        if not self.load_data() or not self.clean_and_prepare_data():
            return False
        
        # 进行4维组合分析
        self.analyze_all_dimension_combinations()
        
        # 找到最佳分类依据
        self.find_optimal_4d_classification_rules()
        
        # 对用户进行分类
        self.classify_users_4d()
        
        # 生成分类报告
        self.generate_4d_classification_report()
        
        return True

def main():
    """主函数"""
    file_path = "副本用户信息表.xlsx"
    
    classifier = FourDimensionClassifier(file_path)
    classifier.run_complete_4d_analysis()

if __name__ == "__main__":
    main()
