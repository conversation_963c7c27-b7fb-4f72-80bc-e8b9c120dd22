"""
增强版用户分类器 - 包含标签区间信息
"""

import pandas as pd
import numpy as np
from user_conversion_analyzer import UserConversionAnalyzer
from typing import Dict, List, Tuple

class EnhancedUserClassifier(UserConversionAnalyzer):
    """增强版用户分类器 - 显示标签区间信息"""
    
    def __init__(self, file_path: str):
        super().__init__(file_path)
        self.dimension_ranges = {}
        self.classification_rules_with_ranges = {}
        self.user_classifications = None
        
    def analyze_dimension_ranges(self):
        """分析每个维度的所有可能值和区间"""
        if not hasattr(self, 'df_clean'):
            print("请先进行数据预处理")
            return
        
        print("\n📊 分析各维度标签区间...")
        
        dimensions = ['mental_score', 'work_status', 'work_years', 'salary_range']
        dimension_names = {
            'mental_score': '心力评分',
            'work_status': '工作状态', 
            'work_years': '工作年限',
            'salary_range': '薪资区间'
        }
        
        self.dimension_ranges = {}
        
        for dim in dimensions:
            unique_values = sorted(self.df_clean[dim].unique())
            # 移除NaN值
            unique_values = [v for v in unique_values if pd.notna(v)]
            
            # 计算每个值的用户数和转化率
            value_stats = []
            for value in unique_values:
                subset = self.df_clean[self.df_clean[dim] == value]
                user_count = len(subset)
                conversion_rate = subset['is_converted'].mean()
                converted_count = subset['is_converted'].sum()
                
                value_stats.append({
                    'value': value,
                    'user_count': user_count,
                    'conversion_rate': conversion_rate,
                    'converted_count': converted_count
                })
            
            self.dimension_ranges[dim] = {
                'name': dimension_names[dim],
                'values': value_stats,
                'total_values': len(unique_values)
            }
            
            print(f"\n{dimension_names[dim]} ({dim}):")
            print(f"  总共 {len(unique_values)} 个不同值")
            for stat in value_stats:
                print(f"    {stat['value']}: {stat['user_count']} 人, 转化率 {stat['conversion_rate']:.1%}")
    
    def find_optimal_classification_with_ranges(self):
        """找到最佳分类依据并包含区间信息"""
        if not hasattr(self, 'results_df'):
            print("请先完成标签组合分析")
            return
        
        print("\n🔍 分析最佳分类依据（含区间信息）...")
        
        # 按转化率排序
        sorted_results = self.results_df.sort_values('conversion_rate', ascending=False)
        
        # 计算分位数阈值
        conversion_rates = sorted_results['conversion_rate'].values
        svip_threshold = max(np.percentile(conversion_rates, 90), 0.15)
        high_threshold = max(np.percentile(conversion_rates, 70), 0.08)
        
        print(f"分类阈值: SVIP≥{svip_threshold:.1%}, 高潜力≥{high_threshold:.1%}")
        
        # 分类组合
        svip_combinations = sorted_results[sorted_results['conversion_rate'] >= svip_threshold]
        high_combinations = sorted_results[
            (sorted_results['conversion_rate'] >= high_threshold) & 
            (sorted_results['conversion_rate'] < svip_threshold)
        ]
        
        # 生成带区间信息的分类规则
        self.classification_rules_with_ranges = {
            'SVIP': self._extract_rules_with_ranges(svip_combinations, 'SVIP'),
            '高潜力': self._extract_rules_with_ranges(high_combinations, '高潜力'),
            '低潜力': []
        }
        
        return self.classification_rules_with_ranges
    
    def _extract_rules_with_ranges(self, combinations_df, level):
        """提取带区间信息的分类规则"""
        rules = []
        
        for _, row in combinations_df.head(5).iterrows():
            combination = row['combination']
            conversion_rate = row['conversion_rate']
            sample_size = row['sample_size']
            
            # 解析组合并添加区间信息
            rule_with_range = self._add_range_info_to_rule(combination, conversion_rate, sample_size, level)
            if rule_with_range:
                rules.append(rule_with_range)
        
        return rules
    
    def _add_range_info_to_rule(self, combination, conversion_rate, sample_size, level):
        """为规则添加区间信息"""
        try:
            rule_info = {
                'original_rule': combination,
                'conversion_rate': conversion_rate,
                'sample_size': sample_size,
                'level': level,
                'dimensions': [],
                'range_description': '',
                'business_interpretation': ''
            }
            
            if ' & ' in combination:
                # 多维度组合
                parts = combination.split(' & ')
                dimensions_info = []
                
                for part in parts:
                    dim, value = part.split('=')
                    dim_info = self._get_dimension_range_info(dim, value)
                    if dim_info:
                        rule_info['dimensions'].append(dim_info)
                        dimensions_info.append(dim_info['description'])
                
                rule_info['range_description'] = ' + '.join(dimensions_info)
            else:
                # 单维度组合
                dim, value = combination.split('=')
                dim_info = self._get_dimension_range_info(dim, value)
                if dim_info:
                    rule_info['dimensions'].append(dim_info)
                    rule_info['range_description'] = dim_info['description']
            
            # 生成业务解释
            rule_info['business_interpretation'] = self._generate_business_interpretation(rule_info)
            
            return rule_info
            
        except Exception as e:
            print(f"处理规则 {combination} 时出错: {e}")
            return None
    
    def _get_dimension_range_info(self, dim, value):
        """获取维度的区间信息"""
        if dim not in self.dimension_ranges:
            return None
        
        dim_data = self.dimension_ranges[dim]
        dim_name = dim_data['name']
        all_values = [v['value'] for v in dim_data['values']]
        
        # 找到当前值在所有值中的位置
        try:
            current_index = all_values.index(value)
            total_count = len(all_values)
            
            # 计算当前值的用户统计
            current_stats = next(v for v in dim_data['values'] if v['value'] == value)
            
            return {
                'dimension': dim,
                'dimension_name': dim_name,
                'value': value,
                'position': f"{current_index + 1}/{total_count}",
                'user_count': current_stats['user_count'],
                'conversion_rate': current_stats['conversion_rate'],
                'all_values': all_values,
                'description': f"{dim_name}={value} (第{current_index + 1}/{total_count}个区间, {current_stats['user_count']}人)"
            }
        except ValueError:
            return None
    
    def _generate_business_interpretation(self, rule_info):
        """生成业务解释"""
        interpretations = []
        
        for dim_info in rule_info['dimensions']:
            dim_name = dim_info['dimension_name']
            value = dim_info['value']
            position = dim_info['position']
            user_count = dim_info['user_count']
            
            if dim_name == '心力评分':
                if isinstance(value, (int, float)):
                    if value >= 4:
                        interpretations.append(f"高心力状态用户({value}分)")
                    elif value >= 2:
                        interpretations.append(f"中等心力状态用户({value}分)")
                    else:
                        interpretations.append(f"低心力状态用户({value}分)")
                else:
                    interpretations.append(f"心力评分{value}")
            
            elif dim_name == '工作状态':
                status_desc = {
                    '在职': '在职稳定群体',
                    '待业': '待业求职群体', 
                    '学生': '学生群体',
                    '已提交离职': '准备离职群体'
                }
                interpretations.append(status_desc.get(value, f"{value}状态"))
            
            elif dim_name == '工作年限':
                if '0-1年' in str(value):
                    interpretations.append("职场新手")
                elif '1-3年' in str(value):
                    interpretations.append("职场新人")
                elif '3-5年' in str(value):
                    interpretations.append("职场骨干")
                elif '5-10年' in str(value):
                    interpretations.append("资深职场人")
                else:
                    interpretations.append(f"工作{value}")
            
            elif dim_name == '薪资区间':
                if '40000以上' in str(value):
                    interpretations.append("高收入群体")
                elif '25001-40000' in str(value):
                    interpretations.append("中高收入群体")
                elif '20001-25000' in str(value):
                    interpretations.append("中等收入群体")
                elif '15001-20000' in str(value):
                    interpretations.append("中低收入群体")
                else:
                    interpretations.append(f"收入{value}")
        
        return " + ".join(interpretations) if interpretations else "特定用户群体"
    
    def classify_users_with_ranges(self):
        """基于区间信息对用户进行分类"""
        if not hasattr(self, 'df_clean'):
            print("请先进行数据预处理")
            return
        
        print("\n👥 基于区间信息进行用户分类...")
        
        # 使用原有的评分方法
        self.df_clean['user_score'] = 0.0
        
        for _, row in self.results_df.iterrows():
            combination = row['combination']
            conversion_rate = row['conversion_rate']
            mask = self._match_combination(combination)
            self.df_clean.loc[mask, 'user_score'] = np.maximum(
                self.df_clean.loc[mask, 'user_score'], 
                conversion_rate
            )
        
        # 分类
        score_percentiles = np.percentile(self.df_clean['user_score'], [70, 90])
        high_threshold = max(score_percentiles[0], 0.08)
        svip_threshold = max(score_percentiles[1], 0.15)
        
        self.df_clean['user_classification'] = '低潜力'
        self.df_clean.loc[self.df_clean['user_score'] >= high_threshold, 'user_classification'] = '高潜力'
        self.df_clean.loc[self.df_clean['user_score'] >= svip_threshold, 'user_classification'] = 'SVIP'
        
        classification_stats = self.df_clean['user_classification'].value_counts()
        
        print(f"用户分类完成:")
        for level, count in classification_stats.items():
            percentage = count / len(self.df_clean) * 100
            print(f"  {level}: {count} 人 ({percentage:.1f}%)")
        
        self.user_classifications = classification_stats
        return classification_stats
    
    def _match_combination(self, combination):
        """匹配组合条件的用户"""
        try:
            if ' & ' in combination:
                parts = combination.split(' & ')
                conditions = []
                for part in parts:
                    dim, val = part.split('=')
                    conditions.append(self.df_clean[dim] == val)
                
                mask = conditions[0]
                for condition in conditions[1:]:
                    mask = mask & condition
                return mask
            else:
                dim, val = combination.split('=')
                return self.df_clean[dim] == val
        except:
            return pd.Series([False] * len(self.df_clean), index=self.df_clean.index)
    
    def generate_enhanced_classification_report(self):
        """生成增强版分类报告"""
        if self.user_classifications is None:
            print("请先完成用户分类")
            return
        
        print("\n" + "="*80)
        print("🎯 用户分类依据报告 (含区间信息)")
        print("="*80)
        
        # 显示维度区间概览
        print(f"\n📊 各维度标签区间概览:")
        for dim, info in self.dimension_ranges.items():
            print(f"\n{info['name']} - 共{info['total_values']}个区间:")
            for i, value_stat in enumerate(info['values'], 1):
                print(f"  {i}. {value_stat['value']}: {value_stat['user_count']} 人 "
                      f"(转化率 {value_stat['conversion_rate']:.1%})")
        
        # 显示分类规则
        print(f"\n🎯 分类依据详解:")
        for level in ['SVIP', '高潜力']:
            if level in self.classification_rules_with_ranges:
                rules = self.classification_rules_with_ranges[level]
                if rules:
                    print(f"\n🏆 {level}用户识别标准:")
                    for i, rule in enumerate(rules, 1):
                        print(f"  {i}. {rule['range_description']}")
                        print(f"     业务特征: {rule['business_interpretation']}")
                        print(f"     转化率: {rule['conversion_rate']:.1%} | 样本量: {rule['sample_size']}")
                        print(f"     原始规则: {rule['original_rule']}")
                        print()
        
        # 显示分类结果统计
        print(f"\n📊 用户分类结果:")
        total_users = len(self.df_clean)
        for level, count in self.user_classifications.items():
            percentage = count / total_users * 100
            level_users = self.df_clean[self.df_clean['user_classification'] == level]
            actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
            print(f"  {level}: {count:,} 人 ({percentage:.1f}%) - 实际转化率: {actual_conversion:.1%}")
        
        # 保存结果
        self._save_enhanced_results()
        print(f"\n💾 增强版分类结果已保存到: 增强版用户分类结果.xlsx")
    
    def _save_enhanced_results(self):
        """保存增强版结果"""
        with pd.ExcelWriter('增强版用户分类结果.xlsx', engine='openpyxl') as writer:
            # 用户分类结果
            output_df = self.df_clean.copy()
            original_columns = {
                'mental_score': '📗心力评分',
                'work_status': '📗工作状态',
                'work_years': '📗工作年限',
                'salary_range': '📗当前薪资区间'
            }
            
            for _, orig_col in original_columns.items():
                if orig_col in self.df.columns:
                    output_df[orig_col] = self.df[orig_col]
            
            columns_order = ['user_classification', 'user_score'] + list(original_columns.values()) + \
                           ['total_amount', 'is_converted']
            existing_columns = [col for col in columns_order if col in output_df.columns]
            output_df[existing_columns].to_excel(writer, sheet_name='用户分类', index=False)
            
            # 维度区间信息
            ranges_data = []
            for dim, info in self.dimension_ranges.items():
                for value_stat in info['values']:
                    ranges_data.append({
                        '维度': info['name'],
                        '维度代码': dim,
                        '标签值': value_stat['value'],
                        '用户数量': value_stat['user_count'],
                        '转化率': f"{value_stat['conversion_rate']:.1%}",
                        '转化用户数': value_stat['converted_count']
                    })
            
            if ranges_data:
                ranges_df = pd.DataFrame(ranges_data)
                ranges_df.to_excel(writer, sheet_name='维度区间信息', index=False)
            
            # 分类规则详情
            rules_data = []
            for level, rules in self.classification_rules_with_ranges.items():
                for rule in rules:
                    rules_data.append({
                        '分类等级': level,
                        '区间描述': rule['range_description'],
                        '业务特征': rule['business_interpretation'],
                        '原始规则': rule['original_rule'],
                        '转化率': f"{rule['conversion_rate']:.1%}",
                        '样本量': rule['sample_size']
                    })
            
            if rules_data:
                rules_df = pd.DataFrame(rules_data)
                rules_df.to_excel(writer, sheet_name='分类规则详情', index=False)
    
    def run_enhanced_classification(self):
        """运行增强版分类流程"""
        print("🚀 开始增强版用户分类分析...")
        
        if not self.load_data() or not self.clean_and_prepare_data():
            return False
        
        # 分析维度区间
        self.analyze_dimension_ranges()
        
        # 进行标签组合分析
        self.analyze_label_combinations()
        self.calculate_potential_score()
        
        # 找到带区间信息的分类依据
        self.find_optimal_classification_with_ranges()
        
        # 对用户进行分类
        self.classify_users_with_ranges()
        
        # 生成增强版报告
        self.generate_enhanced_classification_report()
        
        return True

def main():
    """主函数"""
    file_path = "副本用户信息表.xlsx"
    
    classifier = EnhancedUserClassifier(file_path)
    classifier.run_enhanced_classification()

if __name__ == "__main__":
    main()
