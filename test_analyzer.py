"""
测试文件 - 用户转化潜力分析器
"""

import unittest
import pandas as pd
import numpy as np
import os
from user_conversion_analyzer import UserConversionAnalyzer

class TestUserConversionAnalyzer(unittest.TestCase):
    """用户转化分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试数据
        self.test_data = pd.DataFrame({
            '📗心力评分': [1, 2, 3, 4, 5] * 20,
            '📗工作状态': ['在职', '待业', '学生', '在职', '待业'] * 20,
            '📗工作年限': ['1-3年', '3-5年', '5-10年', '1-3年', '3-5年'] * 20,
            '📗当前薪资区间': ['10000-15000', '15001-20000', '20001-25000', '25001-40000', '40000以上'] * 20,
            '总金额（自动计算）【总金额大于1000为转化】': [0, 500, 1200, 2000, 800] * 20
        })
        
        # 保存测试数据到文件
        self.test_file = 'test_data.xlsx'
        self.test_data.to_excel(self.test_file, index=False)
        
        # 创建分析器实例
        self.analyzer = UserConversionAnalyzer(self.test_file)
    
    def tearDown(self):
        """测试后清理"""
        # 删除测试文件
        if os.path.exists(self.test_file):
            os.remove(self.test_file)
        
        # 删除输出文件
        output_file = "用户转化潜力分析结果.xlsx"
        if os.path.exists(output_file):
            os.remove(output_file)
    
    def test_load_data(self):
        """测试数据加载功能"""
        result = self.analyzer.load_data()
        self.assertTrue(result)
        self.assertIsNotNone(self.analyzer.df)
        self.assertEqual(len(self.analyzer.df), 100)
    
    def test_clean_and_prepare_data(self):
        """测试数据清洗功能"""
        self.analyzer.load_data()
        result = self.analyzer.clean_and_prepare_data()
        self.assertTrue(result)
        self.assertTrue(hasattr(self.analyzer, 'df_clean'))
        
        # 检查转化标签是否正确创建
        expected_conversions = (self.test_data['总金额（自动计算）【总金额大于1000为转化】'] > 1000).sum()
        actual_conversions = self.analyzer.df_clean['is_converted'].sum()
        self.assertEqual(expected_conversions, actual_conversions)
    
    def test_analyze_label_combinations(self):
        """测试标签组合分析功能"""
        self.analyzer.load_data()
        self.analyzer.clean_and_prepare_data()
        result = self.analyzer.analyze_label_combinations()
        
        self.assertIsNotNone(result)
        self.assertTrue(hasattr(self.analyzer, 'results_df'))
        self.assertGreater(len(self.analyzer.results_df), 0)
        
        # 检查必需的列是否存在
        required_columns = ['combination', 'conversion_rate', 'sample_size', 'converted_count', 'dimension_count']
        for col in required_columns:
            self.assertIn(col, self.analyzer.results_df.columns)
    
    def test_calculate_potential_score(self):
        """测试潜力分数计算功能"""
        self.analyzer.load_data()
        self.analyzer.clean_and_prepare_data()
        self.analyzer.analyze_label_combinations()
        result = self.analyzer.calculate_potential_score()
        
        self.assertIsNotNone(result)
        self.assertIn('potential_score', self.analyzer.results_df.columns)
        self.assertIn('potential_level', self.analyzer.results_df.columns)
        
        # 检查潜力等级是否在预期范围内
        valid_levels = ['SVIP', '高', '低']
        for level in self.analyzer.results_df['potential_level'].unique():
            self.assertIn(level, valid_levels)
    
    def test_full_analysis_workflow(self):
        """测试完整分析流程"""
        self.analyzer.load_data()
        self.analyzer.analyze_conversion_potential()
        
        # 检查是否生成了结果
        self.assertTrue(hasattr(self.analyzer, 'results_df'))
        self.assertGreater(len(self.analyzer.results_df), 0)
        
        # 检查输出文件是否生成
        output_file = "用户转化潜力分析结果.xlsx"
        self.assertTrue(os.path.exists(output_file))
    
    def test_invalid_file_path(self):
        """测试无效文件路径处理"""
        invalid_analyzer = UserConversionAnalyzer('nonexistent_file.xlsx')
        result = invalid_analyzer.load_data()
        self.assertFalse(result)
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        # 创建空数据文件
        empty_data = pd.DataFrame()
        empty_file = 'empty_test.xlsx'
        empty_data.to_excel(empty_file, index=False)
        
        try:
            empty_analyzer = UserConversionAnalyzer(empty_file)
            result = empty_analyzer.load_data()
            # 应该能加载但数据为空
            self.assertTrue(result)
            self.assertEqual(len(empty_analyzer.df), 0)
        finally:
            if os.path.exists(empty_file):
                os.remove(empty_file)

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
