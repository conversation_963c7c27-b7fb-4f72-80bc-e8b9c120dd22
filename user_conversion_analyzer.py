import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import os
from typing import Dict, List, Tuple, Any

class UserConversionAnalyzer:
    """用户转化潜力分析器"""
    
    def __init__(self, file_path: str):
        """
        初始化分析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.df = None
        self.analysis_results = {}
        
    def load_data(self) -> bool:
        """
        加载Excel数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"错误：文件 {self.file_path} 不存在")
                return False
                
            # 读取Excel文件
            self.df = pd.read_excel(self.file_path)
            print(f"成功加载数据，共 {len(self.df)} 条记录")
            print(f"数据列名：{list(self.df.columns)}")
            
            # 显示前几行数据以便确认格式
            print("\n数据预览：")
            print(self.df.head())
            
            return True
            
        except Exception as e:
            print(f"加载数据时出错：{str(e)}")
            return False

    def clean_and_prepare_data(self):
        """数据清洗和预处理"""
        if self.df is None:
            return False

        print("开始数据预处理...")

        # 重命名列名，便于处理
        column_mapping = {
            '📗心力评分': 'mental_score',
            '📗工作状态': 'work_status',
            '📗工作年限': 'work_years',
            '📗当前薪资区间': 'salary_range',
            '总金额（自动计算）【总金额大于1000为转化】': 'total_amount'
        }

        # 检查必需列是否存在
        missing_columns = []
        for old_name in column_mapping.keys():
            if old_name not in self.df.columns:
                missing_columns.append(old_name)

        if missing_columns:
            print(f"错误：缺少必需的列: {missing_columns}")
            print(f"可用的列: {list(self.df.columns)}")
            return False

        # 创建工作副本，只选择需要的列
        selected_columns = list(column_mapping.keys())
        self.df_clean = self.df[selected_columns].copy()

        # 重命名列
        self.df_clean.rename(columns=column_mapping, inplace=True)

        # 处理总金额列，转换为数值类型
        print("处理总金额数据...")
        self.df_clean['total_amount'] = pd.to_numeric(self.df_clean['total_amount'], errors='coerce').fillna(0)

        # 创建转化标签
        self.df_clean['is_converted'] = (self.df_clean['total_amount'] > 1000).astype(int)

        # 移除空值
        print("清理空值数据...")
        required_columns = ['mental_score', 'work_status', 'work_years', 'salary_range']
        initial_count = len(self.df_clean)
        self.df_clean = self.df_clean.dropna(subset=required_columns)

        print(f"数据清洗完成:")
        print(f"  原始记录：{initial_count} 条")
        print(f"  有效记录：{len(self.df_clean)} 条")
        print(f"  转化用户数：{self.df_clean['is_converted'].sum()} 人")
        print(f"  总体转化率：{self.df_clean['is_converted'].mean():.2%}")

        # 显示各维度的唯一值数量
        print("\n各维度数据概览:")
        for col in required_columns:
            unique_count = self.df_clean[col].nunique()
            print(f"  {col}: {unique_count} 个不同值")

        return True

    def analyze_label_combinations(self):
        """分析标签组合的转化潜力"""
        if not hasattr(self, 'df_clean'):
            print("请先进行数据预处理")
            return

        print("\n开始分析标签组合...")

        # 分析维度
        dimensions = ['mental_score', 'work_status', 'work_years', 'salary_range']

        # 存储分析结果
        combination_results = []
        min_sample_size = 10  # 最小样本量要求

        print("分析单维度组合...")
        # 分析单维度
        for dim in dimensions:
            print(f"  处理维度: {dim}")
            # 使用groupby提高效率
            grouped = self.df_clean.groupby(dim)['is_converted'].agg(['count', 'sum', 'mean'])

            for value, stats in grouped.iterrows():
                if pd.isna(value) or stats['count'] < min_sample_size:
                    continue

                combination_results.append({
                    'combination': f"{dim}={value}",
                    'conversion_rate': stats['mean'],
                    'sample_size': stats['count'],
                    'converted_count': stats['sum'],
                    'dimension_count': 1
                })

        print("分析二维组合...")
        # 分析二维组合 - 限制组合数量以提高性能
        for i, dim1 in enumerate(dimensions):
            for dim2 in dimensions[i+1:]:
                print(f"  处理组合: {dim1} & {dim2}")
                # 使用groupby提高效率
                grouped = self.df_clean.groupby([dim1, dim2])['is_converted'].agg(['count', 'sum', 'mean'])

                for (val1, val2), stats in grouped.iterrows():
                    if pd.isna(val1) or pd.isna(val2) or stats['count'] < min_sample_size:
                        continue

                    combination_results.append({
                        'combination': f"{dim1}={val1} & {dim2}={val2}",
                        'conversion_rate': stats['mean'],
                        'sample_size': stats['count'],
                        'converted_count': stats['sum'],
                        'dimension_count': 2
                    })

        # 转换为DataFrame便于分析
        self.results_df = pd.DataFrame(combination_results)
        print(f"分析完成，共找到 {len(self.results_df)} 个有效组合")

        return self.results_df

    def calculate_potential_score(self):
        """计算潜力分数并分级"""
        if not hasattr(self, 'results_df'):
            print("请先进行标签组合分析")
            return
        
        print("\n计算潜力分数...")
        
        # 计算潜力分数（综合转化率和样本量）
        # 公式：潜力分数 = 转化率 * log(样本量) * 权重
        self.results_df['potential_score'] = (
            self.results_df['conversion_rate'] * 
            np.log(self.results_df['sample_size'] + 1) * 
            (1 + self.results_df['dimension_count'] * 0.1)  # 多维组合加权
        )
        
        # 按潜力分数排序
        self.results_df = self.results_df.sort_values('potential_score', ascending=False)
        
        # 分级逻辑
        def assign_potential_level(row):
            conversion_rate = row['conversion_rate']
            sample_size = row['sample_size']

            # SVIP: 高转化率 + 足够样本量
            if conversion_rate >= 0.3 and sample_size >= 50:
                return 'SVIP'
            # 高: 中等转化率 + 足够样本量，或高转化率 + 较少样本量
            elif (conversion_rate >= 0.15 and sample_size >= 30) or (conversion_rate >= 0.25 and sample_size >= 20):
                return '高'
            # 低: 其他情况
            else:
                return '低'
        
        self.results_df['potential_level'] = self.results_df.apply(assign_potential_level, axis=1)
        
        return self.results_df

    def generate_report(self):
        """生成分析报告"""
        if not hasattr(self, 'results_df'):
            print("请先完成分析")
            return
        
        print("\n" + "="*60)
        print("用户转化潜力分析报告")
        print("="*60)
        
        # 总体统计
        total_combinations = len(self.results_df)
        svip_count = len(self.results_df[self.results_df['potential_level'] == 'SVIP'])
        high_count = len(self.results_df[self.results_df['potential_level'] == '高'])
        low_count = len(self.results_df[self.results_df['potential_level'] == '低'])
        
        print(f"\n📊 总体统计:")
        print(f"   分析的标签组合总数: {total_combinations}")
        print(f"   SVIP潜力组合: {svip_count} 个")
        print(f"   高潜力组合: {high_count} 个") 
        print(f"   低潜力组合: {low_count} 个")
        
        # 显示TOP 10高潜力组合
        print(f"\n🏆 TOP 10 高潜力标签组合:")
        print("-" * 80)
        top_10 = self.results_df.head(10)
        
        for i, (_, row) in enumerate(top_10.iterrows(), 1):
            print(f"{i:2d}. {row['combination']}")
            print(f"    转化率: {row['conversion_rate']:.2%} | 样本量: {row['sample_size']} | 潜力等级: {row['potential_level']}")
            print()
        
        # 按潜力等级分组显示
        for level in ['SVIP', '高', '低']:
            level_data = self.results_df[self.results_df['potential_level'] == level]
            if len(level_data) > 0:
                print(f"\n🎯 {level}潜力组合 (共{len(level_data)}个):")
                print("-" * 50)
                for _, row in level_data.head(5).iterrows():  # 只显示前5个
                    print(f"   • {row['combination']}")
                    print(f"     转化率: {row['conversion_rate']:.2%} | 样本量: {row['sample_size']}")
        
        # 保存结果到文件
        output_file = "用户转化潜力分析结果.xlsx"
        self.results_df.to_excel(output_file, index=False)
        print(f"\n💾 详细结果已保存到: {output_file}")

    def analyze_conversion_potential(self):
        """执行完整的转化潜力分析流程"""
        if self.df is None:
            print("请先加载数据")
            return
        
        print("开始分析用户转化潜力...")
        
        # 执行分析流程
        if self.clean_and_prepare_data():
            self.analyze_label_combinations()
            self.calculate_potential_score()
            self.generate_report()
        else:
            print("数据预处理失败")

def main():
    """主函数"""
    file_path = "副本用户信息表.xlsx"
    
    # 创建分析器实例
    analyzer = UserConversionAnalyzer(file_path)
    
    # 加载数据
    if analyzer.load_data():
        # 执行分析
        analyzer.analyze_conversion_potential()
    else:
        print("数据加载失败，请检查文件路径和格式")

if __name__ == "__main__":
    main()

