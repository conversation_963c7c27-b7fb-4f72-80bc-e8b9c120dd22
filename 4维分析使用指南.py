"""
4维标签分析使用指南 - 快速查询和分析工具
"""

import pandas as pd
from four_dimension_classifier import FourDimensionClassifier

class FourDimensionAnalyzer:
    """4维分析查询工具"""
    
    def __init__(self, file_path="副本用户信息表.xlsx"):
        self.classifier = FourDimensionClassifier(file_path)
        self.results_loaded = False
    
    def load_results(self):
        """加载4维分析结果"""
        if not self.results_loaded:
            print("🔄 加载4维分析结果...")
            success = self.classifier.run_complete_4d_analysis()
            if success:
                self.results_loaded = True
                print("✅ 4维分析结果加载完成")
            else:
                print("❌ 4维分析结果加载失败")
        return self.results_loaded
    
    def query_best_combinations(self, dimension_count=None, min_conversion_rate=0.15, min_sample_size=10):
        """
        查询最佳组合
        
        Args:
            dimension_count: 维度数量 (1, 2, 3, 4 或 None表示所有)
            min_conversion_rate: 最小转化率
            min_sample_size: 最小样本量
        """
        if not self.load_results():
            return
        
        results = self.classifier.four_dim_results.copy()
        
        # 应用筛选条件
        if dimension_count:
            results = results[results['dimension_count'] == dimension_count]
        
        results = results[
            (results['conversion_rate'] >= min_conversion_rate) &
            (results['sample_size'] >= min_sample_size)
        ]
        
        print(f"\n🎯 最佳{dimension_count if dimension_count else '全'}维组合 (转化率≥{min_conversion_rate:.1%}, 样本量≥{min_sample_size})")
        print("="*80)
        
        if len(results) == 0:
            print("❌ 未找到符合条件的组合")
            return
        
        for i, (_, row) in enumerate(results.head(10).iterrows(), 1):
            print(f"{i}. {row['description']}")
            print(f"   转化率: {row['conversion_rate']:.1%} | 样本量: {row['sample_size']} | {row['dimension_count']}维组合")
            print()
    
    def analyze_specific_user(self, mental_score=None, work_status=None, work_years=None, salary_range=None):
        """
        分析特定用户属性的转化情况
        
        Args:
            mental_score: 心力评分
            work_status: 工作状态
            work_years: 工作年限  
            salary_range: 薪资区间
        """
        if not self.load_results():
            return
        
        # 构建查询条件
        conditions = []
        query_desc = []
        
        if mental_score is not None:
            conditions.append(f"mental_score={mental_score}")
            query_desc.append(f"心力评分={mental_score}")
        
        if work_status is not None:
            conditions.append(f"work_status={work_status}")
            query_desc.append(f"工作状态={work_status}")
        
        if work_years is not None:
            conditions.append(f"work_years={work_years}")
            query_desc.append(f"工作年限={work_years}")
        
        if salary_range is not None:
            conditions.append(f"salary_range={salary_range}")
            query_desc.append(f"薪资区间={salary_range}")
        
        if not conditions:
            print("❌ 请至少指定一个用户属性")
            return
        
        print(f"\n🔍 分析用户: {' + '.join(query_desc)}")
        print("="*60)
        
        # 查找匹配的组合
        matching_combinations = []
        
        for _, row in self.classifier.four_dim_results.iterrows():
            combination = row['combination']
            
            # 检查是否包含所有指定条件
            if all(condition in combination for condition in conditions):
                matching_combinations.append(row)
        
        if not matching_combinations:
            print("❌ 未找到包含这些属性的组合")
            return
        
        # 按转化率排序
        matching_combinations.sort(key=lambda x: x['conversion_rate'], reverse=True)
        
        print(f"📊 找到 {len(matching_combinations)} 个相关组合:")
        
        for i, combo in enumerate(matching_combinations[:5], 1):
            print(f"{i}. {combo['description']}")
            print(f"   转化率: {combo['conversion_rate']:.1%} | 样本量: {combo['sample_size']} | {combo['dimension_count']}维")
            
            # 判断潜力等级
            if combo['conversion_rate'] >= 0.15:
                level = "SVIP"
            elif combo['conversion_rate'] >= 0.08:
                level = "高潜力"
            else:
                level = "低潜力"
            print(f"   潜力等级: {level}")
            print()
    
    def get_classification_summary(self):
        """获取分类总结"""
        if not self.load_results():
            return
        
        print("\n📊 4维分类总结")
        print("="*50)
        
        if hasattr(self.classifier, 'user_classifications'):
            total = sum(self.classifier.user_classifications.values())
            
            for level, count in self.classifier.user_classifications.items():
                percentage = count / total * 100
                level_users = self.classifier.df_clean[self.classifier.df_clean['user_classification_4d'] == level]
                actual_conversion = level_users['is_converted'].mean() if len(level_users) > 0 else 0
                
                print(f"{level}: {count:,} 人 ({percentage:.1f}%)")
                print(f"  实际转化率: {actual_conversion:.1%}")
                print(f"  vs 平均转化率: {actual_conversion/0.055:.1f}x")
                print()
    
    def recommend_marketing_strategy(self, user_count_limit=1000):
        """推荐营销策略"""
        if not self.load_results():
            return
        
        print(f"\n💡 营销策略推荐 (用户数≤{user_count_limit})")
        print("="*60)
        
        # 找到高转化率且用户数适中的组合
        suitable_combinations = self.classifier.four_dim_results[
            (self.classifier.four_dim_results['conversion_rate'] >= 0.12) &
            (self.classifier.four_dim_results['sample_size'] <= user_count_limit) &
            (self.classifier.four_dim_results['sample_size'] >= 20)
        ].head(5)
        
        for i, (_, combo) in enumerate(suitable_combinations.iterrows(), 1):
            print(f"策略 {i}: {combo['description']}")
            print(f"  目标用户数: {combo['sample_size']} 人")
            print(f"  预期转化率: {combo['conversion_rate']:.1%}")
            print(f"  预期转化用户: {int(combo['sample_size'] * combo['conversion_rate'])} 人")
            
            # 投入建议
            if combo['conversion_rate'] >= 0.20:
                investment = "高投入 (个性化服务)"
            elif combo['conversion_rate'] >= 0.15:
                investment = "中高投入 (精准营销)"
            else:
                investment = "中等投入 (标准营销)"
            
            print(f"  投入建议: {investment}")
            print()

def main():
    """主函数 - 演示4维分析使用"""
    analyzer = FourDimensionAnalyzer()
    
    print("🚀 4维标签分析使用指南")
    print("="*50)
    
    # 1. 查询最佳2维组合
    analyzer.query_best_combinations(dimension_count=2, min_conversion_rate=0.15)
    
    # 2. 查询最佳4维组合
    analyzer.query_best_combinations(dimension_count=4, min_conversion_rate=0.20)
    
    # 3. 分析特定用户
    analyzer.analyze_specific_user(
        work_years="1-3年",
        salary_range="25001-40000"
    )
    
    # 4. 分析另一个特定用户
    analyzer.analyze_specific_user(
        mental_score=6,
        work_status="已提交离职"
    )
    
    # 5. 获取分类总结
    analyzer.get_classification_summary()
    
    # 6. 推荐营销策略
    analyzer.recommend_marketing_strategy(user_count_limit=500)

def interactive_query():
    """交互式查询"""
    analyzer = FourDimensionAnalyzer()
    
    print("🔍 4维分析交互式查询")
    print("="*40)
    
    while True:
        print("\n请选择查询类型:")
        print("1. 查询最佳组合")
        print("2. 分析特定用户")
        print("3. 获取分类总结")
        print("4. 营销策略推荐")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            dim = input("维度数量 (1-4, 回车表示所有): ").strip()
            dim = int(dim) if dim.isdigit() and 1 <= int(dim) <= 4 else None
            
            rate = input("最小转化率 (默认15%): ").strip()
            rate = float(rate.rstrip('%'))/100 if rate else 0.15
            
            analyzer.query_best_combinations(dimension_count=dim, min_conversion_rate=rate)
        
        elif choice == '2':
            print("请输入用户属性 (可选填):")
            mental = input("心力评分 (0-10): ").strip()
            mental = int(mental) if mental.isdigit() else None
            
            status = input("工作状态: ").strip() or None
            years = input("工作年限: ").strip() or None
            salary = input("薪资区间: ").strip() or None
            
            analyzer.analyze_specific_user(mental, status, years, salary)
        
        elif choice == '3':
            analyzer.get_classification_summary()
        
        elif choice == '4':
            limit = input("用户数上限 (默认1000): ").strip()
            limit = int(limit) if limit.isdigit() else 1000
            analyzer.recommend_marketing_strategy(limit)
        
        elif choice == '5':
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    # 运行演示
    main()
    
    # 可选: 运行交互式查询
    # interactive_query()
