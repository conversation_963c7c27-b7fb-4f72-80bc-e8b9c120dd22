# 用户转化潜力分析器

基于用户信息表的4个维度标签，分析用户转化潜力，识别高价值用户群体，为业务决策提供数据支持。

## 功能特性

- 📊 **数据导入**: 支持Excel格式的用户数据导入
- 🔍 **标签组合分析**: 分析4个维度的所有可能组合
- 📈 **转化率计算**: 基于总金额>1000的转化标准
- 🎯 **潜力分级**: 输出SVIP、高、低三个等级
- 📋 **结果导出**: 生成详细的分析报告

## 快速开始

### 环境要求

- Python 3.7+
- pandas
- numpy
- openpyxl

### 安装依赖

```bash
pip install pandas numpy openpyxl
```

### 使用方法

1. 准备数据文件（Excel格式），确保包含以下列：
   - 📗心力评分
   - 📗工作状态
   - 📗工作年限
   - 📗当前薪资区间
   - 总金额（自动计算）【总金额大于1000为转化】

2. 运行分析器：

```bash
python user_conversion_analyzer.py
```

3. 查看生成的分析报告：
   - 控制台输出：实时分析结果
   - Excel文件：`用户转化潜力分析结果.xlsx`

## 分析维度

### 四个核心维度

1. **心力评分** (mental_score): 用户心理状态评分
2. **工作状态** (work_status): 当前工作状态
3. **工作年限** (work_years): 工作经验年限
4. **薪资区间** (salary_range): 当前薪资水平

### 潜力分级标准

- **SVIP**: 转化率 ≥ 30% 且样本量 ≥ 50
- **高**: 转化率 ≥ 15% 且样本量 ≥ 30，或转化率 ≥ 25% 且样本量 ≥ 20
- **低**: 其他情况

## 输出结果

### 控制台报告
- 总体统计信息
- TOP 10 高潜力标签组合
- 按潜力等级分组的详细结果

### Excel报告
包含所有分析组合的详细数据：
- combination: 标签组合描述
- conversion_rate: 转化率
- sample_size: 样本量
- converted_count: 转化用户数
- dimension_count: 维度数量
- potential_score: 潜力分数
- potential_level: 潜力等级

## 项目结构

```
.
├── user_conversion_analyzer.py    # 主分析器
├── README.md                      # 项目说明
├── requirements.txt               # 依赖包列表
├── config.py                      # 配置文件
├── test_analyzer.py               # 测试文件
└── 副本用户信息表.xlsx             # 示例数据
```

## 技术实现

- 使用pandas进行高效的数据处理和分组统计
- 采用groupby操作优化大数据集的分析性能
- 实现了灵活的潜力分级算法
- 支持多维度标签组合分析

## 性能优化

- 使用pandas groupby操作替代循环遍历
- 设置最小样本量阈值过滤无效组合
- 优化内存使用，只保留必要的数据列
- 支持大规模数据集（测试支持16000+记录）

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
